# API Documentation

## Overview

The Vue.js and React Dependency Analyzer provides a comprehensive API for analyzing dependency relationships in modern JavaScript/TypeScript projects. The API is designed to be extensible and supports multiple output formats.

## Core Classes

### DependencyGraph

The main class for analyzing project dependencies.

```python
from dependency_graph import DependencyGraph

# Initialize with project path
graph = DependencyGraph("/path/to/project")

# Analyze the project
graph.analyze_project(exclude_patterns=['node_modules', 'dist'])

# Get statistics
stats = graph.get_statistics()
print(f"Total files: {stats['total_files']}")
```

#### Methods

##### `__init__(project_root: str)`

Initialize the dependency graph analyzer.

**Parameters:**
- `project_root` (str): Absolute path to the project root directory

**Example:**
```python
graph = DependencyGraph("/home/<USER>/my-vue-project")
```

##### `analyze_project(exclude_patterns: Optional[List[str]] = None) -> None`

Analyze the entire project and build the dependency graph.

**Parameters:**
- `exclude_patterns` (Optional[List[str]]): Patterns to exclude from analysis

**Default exclusions:** `['node_modules', '.git', 'dist', 'build', '.next', '.nuxt']`

**Example:**
```python
# Use default exclusions
graph.analyze_project()

# Custom exclusions
graph.analyze_project(exclude_patterns=['node_modules', 'coverage', 'docs'])
```

##### `get_statistics() -> Dict[str, int]`

Get comprehensive project statistics.

**Returns:**
- Dictionary containing:
  - `total_files`: Number of analyzed files
  - `total_dependencies`: Number of local dependencies
  - `entry_points`: Number of entry point files
  - `leaf_nodes`: Number of leaf node files
  - `circular_dependencies`: Number of circular dependency cycles
  - `external_packages`: Number of external dependencies

**Example:**
```python
stats = graph.get_statistics()
print(f"Project has {stats['total_files']} files")
print(f"Found {stats['circular_dependencies']} circular dependencies")
```

##### `find_circular_dependencies() -> List[List[str]]`

Find all circular dependencies in the project.

**Returns:**
- List of cycles, where each cycle is a list of file paths

**Example:**
```python
cycles = graph.find_circular_dependencies()
for cycle in cycles:
    print(f"Circular dependency: {' -> '.join(cycle)}")
```

##### `get_dependencies(file_path: str) -> List[str]`

Get all direct dependencies of a specific file.

**Parameters:**
- `file_path` (str): Absolute path to the file

**Returns:**
- List of file paths that the given file depends on

**Example:**
```python
deps = graph.get_dependencies("/project/src/main.ts")
print(f"main.ts depends on: {deps}")
```

##### `get_dependents(file_path: str) -> List[str]`

Get all files that depend on a specific file.

**Parameters:**
- `file_path` (str): Absolute path to the file

**Returns:**
- List of file paths that depend on the given file

**Example:**
```python
dependents = graph.get_dependents("/project/src/components/Button.vue")
print(f"Files that use Button.vue: {dependents}")
```

##### `find_dependency_path(source: str, target: str) -> Optional[List[str]]`

Find the dependency path between two files.

**Parameters:**
- `source` (str): Source file path
- `target` (str): Target file path

**Returns:**
- List of file paths representing the dependency path, or None if no path exists

**Example:**
```python
path = graph.find_dependency_path("/project/src/main.ts", "/project/src/utils/helper.ts")
if path:
    print(f"Dependency path: {' -> '.join(path)}")
```

### Output Formatters

#### JSONFormatter

Format dependency analysis results as structured JSON.

```python
from output_formatters import JSONFormatter

formatter = JSONFormatter(dependency_graph)
json_output = formatter.format(include_details=True)

# Parse the JSON
import json
data = json.loads(json_output)
```

##### `format(include_details: bool = True) -> str`

Generate JSON output.

**Parameters:**
- `include_details` (bool): Include detailed import/export information

**Returns:**
- JSON string with complete dependency analysis

#### DOTFormatter

Format dependency graph as DOT format for Graphviz.

```python
from output_formatters import DOTFormatter

formatter = DOTFormatter(dependency_graph)
dot_output = formatter.format(include_external=False, cluster_by_directory=True)

# Save to file for Graphviz processing
with open("dependencies.dot", "w") as f:
    f.write(dot_output)
```

##### `format(include_external: bool = False, cluster_by_directory: bool = True) -> str`

Generate DOT format output.

**Parameters:**
- `include_external` (bool): Include external dependencies in the graph
- `cluster_by_directory` (bool): Group files by directory

**Returns:**
- DOT format string for Graphviz

#### MermaidFormatter

Format dependency graph as Mermaid diagram syntax.

```python
from output_formatters import MermaidFormatter

formatter = MermaidFormatter(dependency_graph)
mermaid_output = formatter.format(diagram_type='flowchart')
```

##### `format(diagram_type: str = 'flowchart') -> str`

Generate Mermaid diagram syntax.

**Parameters:**
- `diagram_type` (str): Type of diagram ('flowchart' or 'graph')

**Returns:**
- Mermaid diagram syntax string

#### TextTreeFormatter

Format dependency graph as human-readable text tree.

```python
from output_formatters import TextTreeFormatter

formatter = TextTreeFormatter(dependency_graph)
text_output = formatter.format(show_details=True)
print(text_output)
```

##### `format(show_details: bool = False) -> str`

Generate text tree output.

**Parameters:**
- `show_details` (bool): Include detailed dependency information

**Returns:**
- Human-readable text tree representation

## Data Classes

### ImportInfo

Represents information about a single import statement.

```python
from dependency_analyzer import ImportInfo, ImportType, DependencyType

import_info = ImportInfo(
    source_file="/project/src/main.ts",
    imported_module="vue",
    import_type=ImportType.NAMED,
    dependency_type=DependencyType.EXTERNAL,
    imported_names=["createApp"],
    line_number=1,
    is_type_only=False
)
```

**Attributes:**
- `source_file` (str): File containing the import
- `imported_module` (str): Module being imported
- `import_type` (ImportType): Type of import syntax
- `dependency_type` (DependencyType): Category of dependency
- `imported_names` (List[str]): Imported identifiers
- `line_number` (int): Line number of import
- `is_type_only` (bool): TypeScript type-only import

### DependencyNode

Represents a file node in the dependency graph.

```python
from dependency_analyzer import DependencyNode

node = DependencyNode(
    file_path="/project/src/App.vue",
    imports=[...],  # List of ImportInfo objects
    exports=["App"],
    is_entry_point=False,
    is_leaf_node=True
)
```

**Attributes:**
- `file_path` (str): Absolute path to the file
- `imports` (List[ImportInfo]): All imports in the file
- `exports` (List[str]): Exported identifiers
- `is_entry_point` (bool): Whether file is an entry point
- `is_leaf_node` (bool): Whether file has no dependencies

## Enumerations

### ImportType

Types of import statements:
- `DEFAULT`: Default imports (`import App from './App.vue'`)
- `NAMED`: Named imports (`import { createApp } from 'vue'`)
- `NAMESPACE`: Namespace imports (`import * as utils from './utils'`)
- `DYNAMIC`: Dynamic imports (`import('./component')`)
- `REQUIRE`: CommonJS require (`const fs = require('fs')`)

### DependencyType

Categories of dependencies:
- `LOCAL`: Project-relative imports (`./component`, `../utils`)
- `EXTERNAL`: Third-party packages (`vue`, `react`, `lodash`)
- `BUILTIN`: Node.js built-in modules (`fs`, `path`, `crypto`)

## Error Handling

The API handles various error conditions gracefully:

```python
try:
    graph = DependencyGraph("/path/to/project")
    graph.analyze_project()
except Exception as e:
    print(f"Analysis failed: {e}")

# Individual file parsing errors are logged but don't stop analysis
# Check the console output for parsing warnings
```

## Performance Considerations

- Large projects (>1000 files) may take several seconds to analyze
- Exclude unnecessary directories to improve performance
- Use `include_details=False` in JSON formatter for faster output
- Consider using `--exclude` patterns for build artifacts and dependencies
