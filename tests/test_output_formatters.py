"""
Unit tests for the output formatters module.

Tests all formatter classes (JSON, DOT, Mermaid, TextTree) and their ability
to generate different output formats with proper handling of edge cases.
"""

import pytest
import json
from pathlib import Path
from unittest.mock import Mock, MagicMock
import tempfile

from output_formatters import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>atter, D<PERSON><PERSON><PERSON>atter, 
    MermaidFormatter, TextTreeFormatter
)
from dependency_graph import DependencyGraph
from dependency_analyzer import <PERSON>pendencyNode, ImportInfo, ImportType, DependencyType


class TestOutputFormatter:
    """Test the base OutputFormatter class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.mock_graph = Mock(spec=DependencyGraph)
        self.mock_graph.project_root = Path(self.temp_dir)
        self.formatter = OutputFormatter(self.mock_graph)
    
    def test_output_formatter_initialization(self):
        """Test OutputFormatter initialization."""
        assert self.formatter.graph == self.mock_graph
        assert self.formatter.project_root == Path(self.temp_dir)
    
    def test_get_relative_path(self):
        """Test getting relative path from project root."""
        file_path = str(Path(self.temp_dir) / "src" / "components" / "App.vue")
        relative_path = self.formatter._get_relative_path(file_path)
        
        assert relative_path == "src/components/App.vue"
    
    def test_get_relative_path_outside_project(self):
        """Test getting relative path for file outside project."""
        file_path = "/external/path/file.js"
        relative_path = self.formatter._get_relative_path(file_path)
        
        assert relative_path == file_path  # Should return original path
    
    def test_get_file_label(self):
        """Test getting clean file label."""
        file_path = str(Path(self.temp_dir) / "src" / "components" / "App.vue")
        label = self.formatter._get_file_label(file_path)
        
        assert label == "src/components/App.vue"
        assert "\\" not in label  # Should normalize path separators


class TestJSONFormatter:
    """Test the JSONFormatter class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.mock_graph = Mock(spec=DependencyGraph)
        self.mock_graph.project_root = Path(self.temp_dir)
        
        # Create sample nodes
        self.main_node = DependencyNode(
            file_path=str(Path(self.temp_dir) / "src" / "main.ts"),
            imports=[
                ImportInfo(
                    source_file=str(Path(self.temp_dir) / "src" / "main.ts"),
                    imported_module="vue",
                    import_type=ImportType.NAMED,
                    dependency_type=DependencyType.EXTERNAL,
                    imported_names=["createApp"],
                    line_number=1,
                    is_type_only=False
                ),
                ImportInfo(
                    source_file=str(Path(self.temp_dir) / "src" / "main.ts"),
                    imported_module=str(Path(self.temp_dir) / "src" / "App.vue"),
                    import_type=ImportType.DEFAULT,
                    dependency_type=DependencyType.LOCAL,
                    imported_names=["App"],
                    line_number=2,
                    is_type_only=False
                )
            ],
            exports=["main"],
            is_entry_point=True,
            is_leaf_node=False
        )
        
        self.app_node = DependencyNode(
            file_path=str(Path(self.temp_dir) / "src" / "App.vue"),
            imports=[],
            exports=["App"],
            is_entry_point=False,
            is_leaf_node=True
        )
        
        # Configure mock graph
        self.mock_graph.nodes = {
            self.main_node.file_path: self.main_node,
            self.app_node.file_path: self.app_node
        }
        
        self.mock_graph.get_statistics.return_value = {
            "total_files": 2,
            "total_dependencies": 1,
            "entry_points": 1,
            "leaf_nodes": 1,
            "circular_dependencies": 0,
            "external_packages": 1
        }
        
        self.mock_graph.get_external_dependencies.return_value = ["vue"]
        self.mock_graph.find_circular_dependencies.return_value = []
        self.mock_graph.get_entry_points.return_value = [self.main_node.file_path]
        self.mock_graph.get_leaf_nodes.return_value = [self.app_node.file_path]
        self.mock_graph.get_dependencies.return_value = [self.app_node.file_path]
        self.mock_graph.get_dependents.return_value = []
        
        self.formatter = JSONFormatter(self.mock_graph)
    
    def test_json_formatter_initialization(self):
        """Test JSONFormatter initialization."""
        assert self.formatter.graph == self.mock_graph
        assert self.formatter.project_root == Path(self.temp_dir)
    
    def test_format_basic(self):
        """Test basic JSON formatting."""
        result = self.formatter.format(include_details=False)
        
        # Parse JSON to verify structure
        data = json.loads(result)
        
        assert "project_root" in data
        assert "statistics" in data
        assert "files" in data
        assert "external_dependencies" in data
        assert "circular_dependencies" in data
        assert "entry_points" in data
        assert "leaf_nodes" in data
        
        # Check statistics
        assert data["statistics"]["total_files"] == 2
        assert data["statistics"]["entry_points"] == 1
        
        # Check files
        assert len(data["files"]) == 2
        assert "src/main.ts" in data["files"]
        assert "src/App.vue" in data["files"]
        
        # Check entry points and leaf nodes
        assert "src/main.ts" in data["entry_points"]
        assert "src/App.vue" in data["leaf_nodes"]
    
    def test_format_with_details(self):
        """Test JSON formatting with detailed information."""
        result = self.formatter.format(include_details=True)
        
        data = json.loads(result)
        
        # Check that detailed information is included
        main_file = data["files"]["src/main.ts"]
        assert "imports" in main_file
        assert "exports" in main_file
        
        # Check import details
        imports = main_file["imports"]
        assert len(imports) == 2
        
        vue_import = next((imp for imp in imports if imp["module"] == "vue"), None)
        assert vue_import is not None
        assert vue_import["type"] == "named"
        assert vue_import["dependency_type"] == "external"
        assert "createApp" in vue_import["imported_names"]
    
    def test_format_import_info(self):
        """Test formatting import information."""
        import_info = ImportInfo(
            source_file=str(Path(self.temp_dir) / "src" / "main.ts"),
            imported_module=str(Path(self.temp_dir) / "src" / "App.vue"),
            import_type=ImportType.DEFAULT,
            dependency_type=DependencyType.LOCAL,
            imported_names=["App"],
            line_number=5,
            is_type_only=True
        )
        
        formatted = self.formatter._format_import_info(import_info)
        
        assert formatted["module"] == "src/App.vue"  # Should be relative
        assert formatted["type"] == "default"
        assert formatted["dependency_type"] == "local"
        assert formatted["imported_names"] == ["App"]
        assert formatted["line_number"] == 5
        assert formatted["is_type_only"] is True
    
    def test_format_external_import_info(self):
        """Test formatting external import information."""
        import_info = ImportInfo(
            source_file=str(Path(self.temp_dir) / "src" / "main.ts"),
            imported_module="vue",
            import_type=ImportType.NAMED,
            dependency_type=DependencyType.EXTERNAL,
            imported_names=["createApp"],
            line_number=1,
            is_type_only=False
        )
        
        formatted = self.formatter._format_import_info(import_info)
        
        assert formatted["module"] == "vue"  # Should remain as-is for external
        assert formatted["type"] == "named"
        assert formatted["dependency_type"] == "external"
    
    def test_format_with_circular_dependencies(self):
        """Test JSON formatting with circular dependencies."""
        # Mock circular dependencies
        cycle = [
            str(Path(self.temp_dir) / "src" / "A.vue"),
            str(Path(self.temp_dir) / "src" / "B.vue"),
            str(Path(self.temp_dir) / "src" / "A.vue")
        ]
        self.mock_graph.find_circular_dependencies.return_value = [cycle]
        
        result = self.formatter.format()
        data = json.loads(result)
        
        assert len(data["circular_dependencies"]) == 1
        circular_cycle = data["circular_dependencies"][0]
        assert "src/A.vue" in circular_cycle
        assert "src/B.vue" in circular_cycle


class TestDOTFormatter:
    """Test the DOTFormatter class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.mock_graph = Mock(spec=DependencyGraph)
        self.mock_graph.project_root = Path(self.temp_dir)
        
        # Create mock graph structure
        import networkx as nx
        self.mock_nx_graph = nx.DiGraph()
        self.mock_nx_graph.add_edge(
            str(Path(self.temp_dir) / "src" / "main.ts"),
            str(Path(self.temp_dir) / "src" / "App.vue")
        )
        self.mock_graph.graph = self.mock_nx_graph
        
        self.mock_graph.get_entry_points.return_value = [
            str(Path(self.temp_dir) / "src" / "main.ts")
        ]
        self.mock_graph.get_leaf_nodes.return_value = [
            str(Path(self.temp_dir) / "src" / "App.vue")
        ]
        
        self.formatter = DOTFormatter(self.mock_graph)
    
    def test_dot_formatter_initialization(self):
        """Test DOTFormatter initialization."""
        assert self.formatter.graph == self.mock_graph
        assert self.formatter.project_root == Path(self.temp_dir)
    
    def test_format_basic(self):
        """Test basic DOT formatting."""
        result = self.formatter.format()
        
        # Check DOT format structure
        assert result.startswith("digraph DependencyGraph {")
        assert result.endswith("}")
        assert "rankdir=TB;" in result
        assert "node [shape=box, style=filled];" in result
        
        # Check that nodes and edges are included
        assert "src/main.ts" in result
        assert "src/App.vue" in result
        assert "->" in result
    
    def test_format_with_external_dependencies(self):
        """Test DOT formatting including external dependencies."""
        result = self.formatter.format(include_external=True)
        
        # Should include external dependency styling
        assert "digraph DependencyGraph {" in result
    
    def test_format_with_clustering(self):
        """Test DOT formatting with directory clustering."""
        result = self.formatter.format(cluster_by_directory=True)
        
        # Should include clustering information
        assert "digraph DependencyGraph {" in result


class TestMermaidFormatter:
    """Test the MermaidFormatter class."""

    def setup_method(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.mock_graph = Mock(spec=DependencyGraph)
        self.mock_graph.project_root = Path(self.temp_dir)

        # Create mock graph structure
        import networkx as nx
        self.mock_nx_graph = nx.DiGraph()
        self.mock_nx_graph.add_edge(
            str(Path(self.temp_dir) / "src" / "main.ts"),
            str(Path(self.temp_dir) / "src" / "App.vue")
        )
        self.mock_nx_graph.add_edge(
            str(Path(self.temp_dir) / "src" / "App.vue"),
            str(Path(self.temp_dir) / "src" / "components" / "Button.vue")
        )
        self.mock_graph.graph = self.mock_nx_graph

        # Mock nodes
        self.mock_graph.nodes = {
            str(Path(self.temp_dir) / "src" / "main.ts"): Mock(),
            str(Path(self.temp_dir) / "src" / "App.vue"): Mock(),
            str(Path(self.temp_dir) / "src" / "components" / "Button.vue"): Mock()
        }

        self.mock_graph.get_entry_points.return_value = [
            str(Path(self.temp_dir) / "src" / "main.ts")
        ]
        self.mock_graph.get_leaf_nodes.return_value = [
            str(Path(self.temp_dir) / "src" / "components" / "Button.vue")
        ]

        self.formatter = MermaidFormatter(self.mock_graph)

    def test_mermaid_formatter_initialization(self):
        """Test MermaidFormatter initialization."""
        assert self.formatter.graph == self.mock_graph
        assert self.formatter.project_root == Path(self.temp_dir)

    def test_format_flowchart(self):
        """Test Mermaid flowchart formatting."""
        result = self.formatter.format(diagram_type='flowchart')

        # Check flowchart structure
        assert result.startswith("flowchart TD")

        # Check that nodes are defined
        assert "src/main.ts" in result
        assert "src/App.vue" in result
        assert "src/components/Button.vue" in result

        # Check that edges are defined
        assert "-->" in result

        # Check styling
        assert "classDef entryPoint" in result
        assert "classDef leafNode" in result
        assert "classDef regularNode" in result

    def test_format_graph(self):
        """Test Mermaid graph formatting."""
        result = self.formatter.format(diagram_type='graph')

        # Check graph structure
        assert result.startswith("graph TD")

        # Check that relationships are defined
        assert "-->" in result

    def test_format_unsupported_type(self):
        """Test formatting with unsupported diagram type."""
        with pytest.raises(ValueError, match="Unsupported diagram type"):
            self.formatter.format(diagram_type='unsupported')

    def test_format_flowchart_node_styling(self):
        """Test flowchart node styling based on node types."""
        result = self.formatter.format(diagram_type='flowchart')

        # Should include different node shapes for different types
        lines = result.split('\n')

        # Entry points should have specific styling
        entry_point_lines = [line for line in lines if 'class' in line and 'entryPoint' in line]
        assert len(entry_point_lines) > 0

        # Leaf nodes should have specific styling
        leaf_node_lines = [line for line in lines if 'class' in line and 'leafNode' in line]
        assert len(leaf_node_lines) > 0


class TestTextTreeFormatter:
    """Test the TextTreeFormatter class."""

    def setup_method(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.mock_graph = Mock(spec=DependencyGraph)
        self.mock_graph.project_root = Path(self.temp_dir)

        # Create mock graph structure
        import networkx as nx
        self.mock_nx_graph = nx.DiGraph()
        self.mock_nx_graph.add_edge(
            str(Path(self.temp_dir) / "src" / "main.ts"),
            str(Path(self.temp_dir) / "src" / "App.vue")
        )
        self.mock_graph.graph = self.mock_nx_graph

        self.mock_graph.get_statistics.return_value = {
            "total_files": 2,
            "total_dependencies": 1,
            "entry_points": 1,
            "leaf_nodes": 1,
            "circular_dependencies": 0,
            "external_packages": 1
        }

        self.mock_graph.get_entry_points.return_value = [
            str(Path(self.temp_dir) / "src" / "main.ts")
        ]
        self.mock_graph.get_leaf_nodes.return_value = [
            str(Path(self.temp_dir) / "src" / "App.vue")
        ]
        self.mock_graph.find_circular_dependencies.return_value = []
        self.mock_graph.get_dependencies.return_value = [
            str(Path(self.temp_dir) / "src" / "App.vue")
        ]

        self.formatter = TextTreeFormatter(self.mock_graph)

    def test_text_tree_formatter_initialization(self):
        """Test TextTreeFormatter initialization."""
        assert self.formatter.graph == self.mock_graph
        assert self.formatter.project_root == Path(self.temp_dir)

    def test_format_basic(self):
        """Test basic text tree formatting."""
        result = self.formatter.format()

        # Check header
        assert "Dependency Tree Structure" in result
        assert "=" * 50 in result

        # Check statistics section
        assert "Total Files: 2" in result
        assert "Total Dependencies: 1" in result
        assert "Entry Points: 1" in result
        assert "Leaf Nodes: 1" in result

        # Check entry points section
        assert "Entry Points:" in result
        assert "src/main.ts" in result

    def test_format_with_details(self):
        """Test text tree formatting with detailed information."""
        result = self.formatter.format(show_details=True)

        # Should include detailed dependency information
        assert "Dependency Tree Structure" in result
        assert "src/main.ts" in result
        assert "src/App.vue" in result

    def test_format_with_circular_dependencies(self):
        """Test text tree formatting with circular dependencies."""
        # Mock circular dependencies
        cycle = [
            str(Path(self.temp_dir) / "src" / "A.vue"),
            str(Path(self.temp_dir) / "src" / "B.vue"),
            str(Path(self.temp_dir) / "src" / "A.vue")
        ]
        self.mock_graph.find_circular_dependencies.return_value = [cycle]

        # Update statistics
        stats = self.mock_graph.get_statistics.return_value.copy()
        stats["circular_dependencies"] = 1
        self.mock_graph.get_statistics.return_value = stats

        result = self.formatter.format()

        # Should include circular dependency information
        assert "Circular Dependencies: 1" in result
        assert "Circular Dependencies Found:" in result

    def test_format_dependency_tree(self):
        """Test formatting dependency tree structure."""
        # This tests the internal tree formatting method
        result_lines = self.formatter._format_dependency_tree(
            str(Path(self.temp_dir) / "src" / "main.ts"),
            show_details=False,
            visited=set(),
            prefix=""
        )

        assert len(result_lines) > 0
        assert any("main.ts" in line for line in result_lines)

    def test_format_dependency_tree_circular(self):
        """Test formatting dependency tree with circular reference."""
        # Create a circular reference in the mock graph
        self.mock_nx_graph.add_edge(
            str(Path(self.temp_dir) / "src" / "App.vue"),
            str(Path(self.temp_dir) / "src" / "main.ts")
        )

        # Mock get_dependencies to return circular dependency
        def mock_get_dependencies(file_path):
            if "main.ts" in file_path:
                return [str(Path(self.temp_dir) / "src" / "App.vue")]
            elif "App.vue" in file_path:
                return [str(Path(self.temp_dir) / "src" / "main.ts")]
            return []

        self.mock_graph.get_dependencies.side_effect = mock_get_dependencies

        result_lines = self.formatter._format_dependency_tree(
            str(Path(self.temp_dir) / "src" / "main.ts"),
            show_details=False,
            visited=set(),
            prefix=""
        )

        # Should handle circular reference gracefully
        assert len(result_lines) > 0
        # Should detect circular reference when visiting the same node again
        result_lines_with_visited = self.formatter._format_dependency_tree(
            str(Path(self.temp_dir) / "src" / "main.ts"),
            show_details=False,
            visited={str(Path(self.temp_dir) / "src" / "main.ts")},
            prefix=""
        )

        assert any("circular" in line for line in result_lines_with_visited)


class TestOutputFormattersIntegration:
    """Integration tests for output formatters."""

    def setup_method(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()

        # Create a real dependency graph for integration testing
        self.graph = DependencyGraph(self.temp_dir)

        # Add some mock data
        main_node = DependencyNode(
            file_path=str(Path(self.temp_dir) / "src" / "main.ts"),
            imports=[
                ImportInfo(
                    source_file=str(Path(self.temp_dir) / "src" / "main.ts"),
                    imported_module="vue",
                    import_type=ImportType.NAMED,
                    dependency_type=DependencyType.EXTERNAL,
                    imported_names=["createApp"],
                    line_number=1
                )
            ],
            exports=["main"],
            is_entry_point=True,
            is_leaf_node=False
        )

        app_node = DependencyNode(
            file_path=str(Path(self.temp_dir) / "src" / "App.vue"),
            imports=[],
            exports=["App"],
            is_entry_point=False,
            is_leaf_node=True
        )

        self.graph.nodes[main_node.file_path] = main_node
        self.graph.nodes[app_node.file_path] = app_node
        self.graph.graph.add_node(main_node.file_path)
        self.graph.graph.add_node(app_node.file_path)
        self.graph.graph.add_edge(main_node.file_path, app_node.file_path)

    def test_all_formatters_produce_output(self):
        """Test that all formatters produce valid output."""
        # JSON formatter
        json_formatter = JSONFormatter(self.graph)
        json_result = json_formatter.format()
        assert len(json_result) > 0
        # Should be valid JSON
        json.loads(json_result)

        # DOT formatter
        dot_formatter = DOTFormatter(self.graph)
        dot_result = dot_formatter.format()
        assert len(dot_result) > 0
        assert "digraph" in dot_result

        # Mermaid formatter
        mermaid_formatter = MermaidFormatter(self.graph)
        mermaid_result = mermaid_formatter.format()
        assert len(mermaid_result) > 0
        assert "flowchart" in mermaid_result

        # Text tree formatter
        text_formatter = TextTreeFormatter(self.graph)
        text_result = text_formatter.format()
        assert len(text_result) > 0
        assert "Dependency Tree Structure" in text_result

    def test_formatters_handle_empty_graph(self):
        """Test that formatters handle empty graphs gracefully."""
        empty_graph = DependencyGraph(self.temp_dir)

        # All formatters should handle empty graphs without errors
        json_formatter = JSONFormatter(empty_graph)
        json_result = json_formatter.format()
        assert len(json_result) > 0

        dot_formatter = DOTFormatter(empty_graph)
        dot_result = dot_formatter.format()
        assert len(dot_result) > 0

        mermaid_formatter = MermaidFormatter(empty_graph)
        mermaid_result = mermaid_formatter.format()
        assert len(mermaid_result) > 0

        text_formatter = TextTreeFormatter(empty_graph)
        text_result = text_formatter.format()
        assert len(text_result) > 0
