"""
React/TypeScript File Parser

Specialized parser for React components in .jsx, .tsx, and .ts files.
This module provides comprehensive parsing capabilities for React projects,
including JSX analysis, hook detection, component pattern recognition,
and TypeScript support.

The parser handles:
- Function and class React components
- React hooks usage analysis
- JSX component usage detection
- TypeScript interface and type definitions
- PropTypes and component metadata
- Custom hooks and utility functions
"""

import re
from typing import List, Optional, Dict, Set, Tuple
from pathlib import Path

from dependency_analyzer import BaseParser, DependencyNode, ImportInfo, ImportType, DependencyType


class ReactParser(BaseParser):
    """
    Specialized parser for React components and TypeScript files.

    This parser extends BaseParser to handle React-specific syntax and patterns,
    including JSX, React hooks, component definitions, and TypeScript features.

    Attributes:
        react_hooks (Set[str]): Set of known React hook names for detection
        react_component_patterns (List[str]): Regex patterns for detecting React components

    Examples:
        >>> parser = ReactParser()
        >>> node = parser.parse_file("/src/components/UserProfile.tsx")
        >>> if node:
        ...     print(f"Found {len(node.imports)} imports")
        ...     react_info = parser.get_react_specific_info(node.file_path)
        ...     print(f"Uses hooks: {react_info['uses_hooks']}")
    """

    def __init__(self):
        """
        Initialize the React parser with React-specific patterns and hooks.

        Sets up React hook detection, component patterns, and inherits
        tree-sitter parsers from BaseParser for TypeScript and JSX support.
        """
        super().__init__()
        self.react_hooks = {
            'useState', 'useEffect', 'useContext', 'useReducer', 'useCallback',
            'useMemo', 'useRef', 'useImperativeHandle', 'useLayoutEffect',
            'useDebugValue', 'useDeferredValue', 'useId', 'useInsertionEffect',
            'useSyncExternalStore', 'useTransition'
        }

        self.react_component_patterns = [
            r'function\s+([A-Z][a-zA-Z0-9]*)\s*\(',  # Function components
            r'const\s+([A-Z][a-zA-Z0-9]*)\s*=\s*\(',  # Arrow function components
            r'class\s+([A-Z][a-zA-Z0-9]*)\s+extends\s+(?:React\.)?Component',  # Class components
        ]

    def parse_file(self, file_path: str) -> Optional[DependencyNode]:
        """
        Parse a React/TypeScript file and extract dependency information.

        Processes React component files by analyzing imports, JSX usage,
        component definitions, and React-specific patterns like hooks.

        Args:
            file_path (str): Absolute path to the React file to parse

        Returns:
            Optional[DependencyNode]: Parsed dependency information or None if parsing fails

        Examples:
            >>> parser = ReactParser()
            >>> node = parser.parse_file("/src/components/Button.tsx")
            >>> if node:
            ...     print(f"Component exports: {node.exports}")
            ...     print(f"Has {len(node.imports)} dependencies")
        """
        try:
            content = self._read_file_content(file_path)
            
            # Get the appropriate parser
            parser = self._get_parser_for_file(file_path)
            tree = parser.parse(content.encode('utf-8'))
            
            # Extract imports using tree-sitter
            imports = self._extract_imports_from_tree(tree, content, file_path)
            
            # Extract React-specific imports and usage
            react_imports = self._extract_react_specific_imports(content, file_path)
            imports.extend(react_imports)
            
            # Extract JSX component usage
            jsx_imports = self._extract_jsx_component_usage(tree, content, file_path)
            imports.extend(jsx_imports)
            
            # Extract exports
            exports = self._extract_exports_from_tree(tree, content)
            
            # Extract React component definitions
            component_exports = self._extract_react_components(content)
            exports.extend(component_exports)
            
            return DependencyNode(
                file_path=file_path,
                imports=imports,
                exports=exports,
                is_entry_point=self._is_entry_point(file_path),
                is_leaf_node=len(imports) == 0
            )
            
        except Exception as e:
            print(f"Error parsing React file {file_path}: {e}")
            return None
    
    def _extract_react_specific_imports(self, content: str, source_file: str) -> List[ImportInfo]:
        """Extract React-specific imports like hooks and components."""
        imports = []
        
        # Look for React hook imports
        hook_import_pattern = r'import\s*\{\s*([^}]+)\s*\}\s*from\s*[\'"]react[\'"]'
        matches = re.findall(hook_import_pattern, content)
        
        for match in matches:
            # Extract individual hook names
            hook_names = [name.strip() for name in match.split(',')]
            
            # Filter to only include actual React hooks
            react_hooks_used = [name for name in hook_names if name in self.react_hooks]
            
            if react_hooks_used:
                resolved_path, dep_type = self._resolve_import_path("'react'", source_file)
                
                imports.append(ImportInfo(
                    source_file=source_file,
                    imported_module=resolved_path,
                    import_type=ImportType.NAMED,
                    dependency_type=dep_type,
                    imported_names=react_hooks_used,
                    line_number=self._get_line_number(content, content.find(match))
                ))
        
        # Look for React default import
        react_default_pattern = r'import\s+React\s+from\s*[\'"]react[\'"]'
        if re.search(react_default_pattern, content):
            resolved_path, dep_type = self._resolve_import_path("'react'", source_file)
            
            imports.append(ImportInfo(
                source_file=source_file,
                imported_module=resolved_path,
                import_type=ImportType.DEFAULT,
                dependency_type=dep_type,
                imported_names=['React'],
                line_number=self._get_line_number(content, content.find('import React'))
            ))
        
        return imports
    
    def _extract_jsx_component_usage(self, tree, content: str, source_file: str) -> List[ImportInfo]:
        """Extract JSX component usage from the parse tree."""
        imports = []
        used_components = set()

        # Walk the tree to find JSX elements
        self._walk_tree_for_jsx(tree.root_node, content, used_components)

        # For each used component, try to find its import
        for component_name in used_components:
            import_info = self._find_component_import(content, component_name, source_file)
            if import_info:
                imports.append(import_info)

        return imports

    def _walk_tree_for_jsx(self, node, content: str, used_components: set):
        """Walk the tree recursively to find JSX component usage."""
        if node.type in ["jsx_element", "jsx_self_closing_element"]:
            # Find the component name
            for child in node.children:
                if child.type in ["jsx_opening_element", "jsx_closing_element"]:
                    for grandchild in child.children:
                        if grandchild.type == "identifier":
                            component_name = content[grandchild.start_byte:grandchild.end_byte]
                            # Only consider PascalCase components (custom components)
                            if component_name and component_name[0].isupper():
                                used_components.add(component_name)
                            break
                elif child.type == "identifier":
                    component_name = content[child.start_byte:child.end_byte]
                    # Only consider PascalCase components (custom components)
                    if component_name and component_name[0].isupper():
                        used_components.add(component_name)

        # Recursively process children
        for child in node.children:
            self._walk_tree_for_jsx(child, content, used_components)
    
    def _find_component_import(self, content: str, component_name: str, source_file: str) -> Optional[ImportInfo]:
        """Find the import statement for a given component name."""
        # Look for default import
        default_pattern = rf'import\s+{component_name}\s+from\s+[\'"]([^\'"]+)[\'"]'
        match = re.search(default_pattern, content)
        
        if match:
            import_path = match.group(1)
            resolved_path, dep_type = self._resolve_import_path(f"'{import_path}'", source_file)
            
            return ImportInfo(
                source_file=source_file,
                imported_module=resolved_path,
                import_type=ImportType.DEFAULT,
                dependency_type=dep_type,
                imported_names=[component_name],
                line_number=self._get_line_number(content, match.start())
            )
        
        # Look for named import
        named_pattern = rf'import\s*\{{\s*[^}}]*{component_name}[^}}]*\}}\s*from\s+[\'"]([^\'"]+)[\'"]'
        match = re.search(named_pattern, content)
        
        if match:
            import_path = match.group(1)
            resolved_path, dep_type = self._resolve_import_path(f"'{import_path}'", source_file)
            
            return ImportInfo(
                source_file=source_file,
                imported_module=resolved_path,
                import_type=ImportType.NAMED,
                dependency_type=dep_type,
                imported_names=[component_name],
                line_number=self._get_line_number(content, match.start())
            )
        
        return None
    
    def _extract_exports_from_tree(self, tree, content: str) -> List[str]:
        """Extract export statements from the parse tree."""
        exports = []

        # Walk the tree to find export statements
        self._walk_tree_for_exports(tree.root_node, content, exports)

        return exports

    def _walk_tree_for_exports(self, node, content: str, exports: List[str]):
        """Walk the tree recursively to find export statements."""
        if node.type in ["export_statement", "export_assignment"]:
            export_text = content[node.start_byte:node.end_byte]
            exports.append(export_text)

        # Recursively process children
        for child in node.children:
            self._walk_tree_for_exports(child, content, exports)
    
    def _extract_react_components(self, content: str) -> List[str]:
        """Extract React component definitions."""
        components = []
        
        for pattern in self.react_component_patterns:
            matches = re.findall(pattern, content)
            components.extend(matches)
        
        return components
    
    def _get_line_number(self, content: str, position: int) -> int:
        """Get line number for a given character position in content."""
        return content[:position].count('\n') + 1
    
    def _is_entry_point(self, file_path: str) -> bool:
        """Determine if this file is likely an entry point."""
        path = Path(file_path)
        
        # Common entry point patterns for React apps
        entry_patterns = [
            'index.tsx',
            'index.jsx',
            'index.ts',
            'main.tsx',
            'main.jsx',
            'app.tsx',
            'app.jsx'
        ]
        
        return (
            path.name.lower() in entry_patterns or
            'main' in path.name.lower() or
            'app' in path.name.lower() or
            path.parent.name == 'src' and path.name.startswith('index')
        )
    
    def get_react_specific_info(self, file_path: str) -> Dict:
        """Extract React-specific information like hooks usage, component type, etc."""
        try:
            content = self._read_file_content(file_path)
            
            react_info = {
                'uses_hooks': self._uses_react_hooks(content),
                'hooks_used': self._get_hooks_used(content),
                'component_type': self._get_component_type(content),
                'has_jsx': self._has_jsx(content),
                'uses_typescript': Path(file_path).suffix in ['.tsx', '.ts'],
                'prop_types': self._extract_prop_types(content),
                'state_usage': self._has_state_usage(content)
            }
            
            return react_info
            
        except Exception as e:
            print(f"Error extracting React info from {file_path}: {e}")
            return {}
    
    def _uses_react_hooks(self, content: str) -> bool:
        """Check if the file uses React hooks."""
        for hook in self.react_hooks:
            if re.search(rf'\b{hook}\s*\(', content):
                return True
        return False
    
    def _get_hooks_used(self, content: str) -> List[str]:
        """Get list of React hooks used in the file."""
        hooks_used = []
        for hook in self.react_hooks:
            if re.search(rf'\b{hook}\s*\(', content):
                hooks_used.append(hook)
        return hooks_used
    
    def _get_component_type(self, content: str) -> str:
        """Determine the type of React component (functional, class, or mixed)."""
        has_functional = bool(re.search(r'function\s+[A-Z][a-zA-Z0-9]*\s*\(', content))
        has_arrow_functional = bool(re.search(r'const\s+[A-Z][a-zA-Z0-9]*\s*=\s*\(', content))
        has_class = bool(re.search(r'class\s+[A-Z][a-zA-Z0-9]*\s+extends\s+(?:React\.)?Component', content))
        
        if has_class and (has_functional or has_arrow_functional):
            return 'mixed'
        elif has_class:
            return 'class'
        elif has_functional or has_arrow_functional:
            return 'functional'
        else:
            return 'none'
    
    def _has_jsx(self, content: str) -> bool:
        """Check if the file contains JSX."""
        jsx_patterns = [
            r'<[A-Z][a-zA-Z0-9]*',  # JSX component tags
            r'<[a-z]+[^>]*>',       # HTML tags
            r'return\s*\(',         # Common JSX return pattern
            r'jsx',                 # JSX pragma
        ]
        
        return any(re.search(pattern, content) for pattern in jsx_patterns)
    
    def _extract_prop_types(self, content: str) -> List[str]:
        """Extract PropTypes or TypeScript interface definitions."""
        prop_types = []
        
        # TypeScript interface props
        interface_pattern = r'interface\s+(\w*Props?)\s*\{'
        matches = re.findall(interface_pattern, content)
        prop_types.extend(matches)
        
        # TypeScript type props
        type_pattern = r'type\s+(\w*Props?)\s*='
        matches = re.findall(type_pattern, content)
        prop_types.extend(matches)
        
        # PropTypes
        proptypes_pattern = r'(\w+)\.propTypes\s*='
        matches = re.findall(proptypes_pattern, content)
        prop_types.extend(matches)
        
        return prop_types
    
    def _has_state_usage(self, content: str) -> bool:
        """Check if the component uses state."""
        state_patterns = [
            r'useState\s*\(',
            r'this\.state',
            r'setState\s*\(',
            r'useReducer\s*\(',
        ]
        
        return any(re.search(pattern, content) for pattern in state_patterns)
