"""
Unit tests for the core dependency analyzer module.

Tests the BaseParser class, ImportInfo and DependencyNode dataclasses,
and core parsing functionality.
"""

import pytest
from pathlib import Path
from unittest.mock import Mock, patch, mock_open
import tempfile
import os

from dependency_analyzer import (
    BaseParser, ImportInfo, DependencyNode, ImportType, DependencyType
)


class TestImportType:
    """Test the ImportType enum."""
    
    def test_import_type_values(self):
        """Test that ImportType has expected values."""
        assert ImportType.DEFAULT.value == "default"
        assert ImportType.NAMED.value == "named"
        assert ImportType.NAMESPACE.value == "namespace"
        assert ImportType.DYNAMIC.value == "dynamic"
        assert ImportType.REQUIRE.value == "require"


class TestDependencyType:
    """Test the DependencyType enum."""
    
    def test_dependency_type_values(self):
        """Test that DependencyType has expected values."""
        assert DependencyType.LOCAL.value == "local"
        assert DependencyType.EXTERNAL.value == "external"
        assert DependencyType.BUILTIN.value == "builtin"


class TestImportInfo:
    """Test the ImportInfo dataclass."""
    
    def test_import_info_creation(self):
        """Test creating an ImportInfo instance."""
        import_info = ImportInfo(
            source_file="/test/src/main.ts",
            imported_module="vue",
            import_type=ImportType.NAMED,
            dependency_type=DependencyType.EXTERNAL,
            imported_names=["createApp", "ref"],
            line_number=1,
            is_type_only=False
        )
        
        assert import_info.source_file == "/test/src/main.ts"
        assert import_info.imported_module == "vue"
        assert import_info.import_type == ImportType.NAMED
        assert import_info.dependency_type == DependencyType.EXTERNAL
        assert import_info.imported_names == ["createApp", "ref"]
        assert import_info.line_number == 1
        assert import_info.is_type_only is False
    
    def test_import_info_defaults(self):
        """Test ImportInfo with default values."""
        import_info = ImportInfo(
            source_file="/test/src/main.ts",
            imported_module="vue",
            import_type=ImportType.NAMED,
            dependency_type=DependencyType.EXTERNAL,
            imported_names=["createApp"],
            line_number=1
        )
        
        assert import_info.is_type_only is False  # Default value
    
    def test_import_info_type_only(self):
        """Test ImportInfo with type-only import."""
        import_info = ImportInfo(
            source_file="/test/src/main.ts",
            imported_module="./types",
            import_type=ImportType.NAMED,
            dependency_type=DependencyType.LOCAL,
            imported_names=["User"],
            line_number=2,
            is_type_only=True
        )
        
        assert import_info.is_type_only is True


class TestDependencyNode:
    """Test the DependencyNode dataclass."""
    
    def test_dependency_node_creation(self):
        """Test creating a DependencyNode instance."""
        imports = [
            ImportInfo(
                source_file="/test/src/App.vue",
                imported_module="vue",
                import_type=ImportType.NAMED,
                dependency_type=DependencyType.EXTERNAL,
                imported_names=["defineComponent"],
                line_number=1
            )
        ]
        
        node = DependencyNode(
            file_path="/test/src/App.vue",
            imports=imports,
            exports=["App"],
            is_entry_point=False,
            is_leaf_node=False
        )
        
        assert node.file_path == "/test/src/App.vue"
        assert len(node.imports) == 1
        assert node.imports[0].imported_module == "vue"
        assert node.exports == ["App"]
        assert node.is_entry_point is False
        assert node.is_leaf_node is False
    
    def test_dependency_node_defaults(self):
        """Test DependencyNode with default values."""
        node = DependencyNode(
            file_path="/test/src/App.vue",
            imports=[],
            exports=[]
        )
        
        assert node.is_entry_point is False  # Default value
        assert node.is_leaf_node is False    # Default value
    
    def test_dependency_node_entry_point(self):
        """Test DependencyNode as entry point."""
        node = DependencyNode(
            file_path="/test/src/main.ts",
            imports=[],
            exports=["main"],
            is_entry_point=True,
            is_leaf_node=False
        )
        
        assert node.is_entry_point is True
    
    def test_dependency_node_leaf_node(self):
        """Test DependencyNode as leaf node."""
        node = DependencyNode(
            file_path="/test/src/utils/helper.ts",
            imports=[],
            exports=["helper"],
            is_entry_point=False,
            is_leaf_node=True
        )
        
        assert node.is_leaf_node is True


class TestBaseParser:
    """Test the BaseParser class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.parser = BaseParser()
    
    def test_base_parser_initialization(self):
        """Test BaseParser initialization."""
        assert self.parser.ts_language is not None
        assert self.parser.tsx_language is not None
        assert self.parser.ts_parser is not None
        assert self.parser.tsx_parser is not None
    
    def test_parse_file_not_implemented(self):
        """Test that parse_file raises NotImplementedError."""
        with pytest.raises(NotImplementedError):
            self.parser.parse_file("/test/file.ts")
    
    @patch('builtins.open', new_callable=mock_open, read_data="test content")
    def test_read_file_content(self, mock_file):
        """Test reading file content."""
        content = self.parser._read_file_content("/test/file.ts")
        assert content == "test content"
        mock_file.assert_called_once_with("/test/file.ts", 'r', encoding='utf-8')
    
    @patch('builtins.open', side_effect=FileNotFoundError())
    def test_read_file_content_file_not_found(self, mock_file):
        """Test reading non-existent file."""
        content = self.parser._read_file_content("/nonexistent/file.ts")
        assert content == ""
    
    @patch('builtins.open', side_effect=UnicodeDecodeError('utf-8', b'', 0, 1, 'invalid'))
    def test_read_file_content_unicode_error(self, mock_file):
        """Test reading file with unicode error."""
        content = self.parser._read_file_content("/test/file.ts")
        assert content == ""
    
    def test_resolve_import_path_relative(self):
        """Test resolving relative import paths."""
        source_file = "/test/src/components/UserProfile.vue"
        
        # Test relative import
        resolved, dep_type = self.parser._resolve_import_path("./UserCard.vue", source_file)
        assert resolved == "/test/src/components/UserCard.vue"
        assert dep_type == DependencyType.LOCAL
        
        # Test parent directory import
        resolved, dep_type = self.parser._resolve_import_path("../common/Button.vue", source_file)
        assert resolved == "/test/src/common/Button.vue"
        assert dep_type == DependencyType.LOCAL
    
    def test_resolve_import_path_absolute(self):
        """Test resolving absolute import paths."""
        source_file = "/test/src/components/UserProfile.vue"
        
        # Test absolute import with @/ alias
        with patch.object(self.parser, '_find_project_root', return_value=Path("/test")):
            resolved, dep_type = self.parser._resolve_import_path("@/types/user", source_file)
            assert resolved == "/test/src/types/user"
            assert dep_type == DependencyType.LOCAL
    
    def test_resolve_import_path_external(self):
        """Test resolving external package imports."""
        source_file = "/test/src/components/UserProfile.vue"
        
        # Test external package
        resolved, dep_type = self.parser._resolve_import_path("vue", source_file)
        assert resolved == "vue"
        assert dep_type == DependencyType.EXTERNAL
        
        # Test scoped package
        resolved, dep_type = self.parser._resolve_import_path("@vue/reactivity", source_file)
        assert resolved == "@vue/reactivity"
        assert dep_type == DependencyType.EXTERNAL
    
    def test_resolve_import_path_builtin(self):
        """Test resolving built-in module imports."""
        source_file = "/test/src/utils/helper.ts"
        
        # Test Node.js built-in modules
        for builtin in ["fs", "path", "os", "crypto", "util"]:
            resolved, dep_type = self.parser._resolve_import_path(builtin, source_file)
            assert resolved == builtin
            assert dep_type == DependencyType.BUILTIN
    
    def test_find_project_root(self):
        """Test finding project root directory."""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Create project structure
            src_dir = temp_path / "src" / "components"
            src_dir.mkdir(parents=True)
            
            # Create package.json to indicate project root
            (temp_path / "package.json").write_text('{"name": "test"}')
            
            # Test finding root from nested directory
            test_file = src_dir / "Component.vue"
            test_file.write_text("<template></template>")
            
            root = self.parser._find_project_root(str(test_file))
            assert root == temp_path
    
    def test_find_project_root_no_indicators(self):
        """Test finding project root when no indicators exist."""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            test_file = temp_path / "file.ts"
            test_file.write_text("// test")
            
            root = self.parser._find_project_root(str(test_file))
            assert root is None
    
    def test_is_entry_point(self):
        """Test entry point detection."""
        # Test common entry point files
        assert self.parser._is_entry_point("/test/src/main.ts") is True
        assert self.parser._is_entry_point("/test/src/index.tsx") is True
        assert self.parser._is_entry_point("/test/src/app.js") is True
        assert self.parser._is_entry_point("/test/src/App.vue") is True
        
        # Test non-entry point files
        assert self.parser._is_entry_point("/test/src/components/Button.vue") is False
        assert self.parser._is_entry_point("/test/src/utils/helper.ts") is False
    
    def test_clean_import_path(self):
        """Test cleaning import paths."""
        # Test removing quotes
        assert self.parser._clean_import_path('"./Component.vue"') == "./Component.vue"
        assert self.parser._clean_import_path("'./Component.vue'") == "./Component.vue"
        
        # Test removing file extensions
        assert self.parser._clean_import_path("./Component.vue") == "./Component.vue"
        assert self.parser._clean_import_path("./utils/helper.ts") == "./utils/helper.ts"
        
        # Test already clean paths
        assert self.parser._clean_import_path("vue") == "vue"
        assert self.parser._clean_import_path("@/types/user") == "@/types/user"

    def test_extract_imports_from_tree(self):
        """Test extracting imports from tree-sitter parse tree."""
        # Sample TypeScript code with various import types
        source_code = '''
import { createApp } from 'vue';
import type { User } from './types/user';
import Button from './components/Button.vue';
import * as utils from './utils';
const helper = require('./helper');
'''

        # Parse with TypeScript parser
        tree = self.parser.ts_parser.parse(source_code.encode('utf-8'))
        imports = self.parser._extract_imports_from_tree(tree, source_code, "/test/src/main.ts")

        # Should find multiple imports
        assert len(imports) >= 3

        # Check that we found the named import
        vue_import = next((imp for imp in imports if imp.imported_module == "vue"), None)
        assert vue_import is not None
        assert vue_import.import_type == ImportType.NAMED
        assert "createApp" in vue_import.imported_names

    def test_extract_exports_from_tree(self):
        """Test extracting exports from tree-sitter parse tree."""
        # Sample TypeScript code with various export types
        source_code = '''
export const helper = () => {};
export default class Component {};
export { utils } from './utils';
export type User = { name: string };
'''

        # Parse with TypeScript parser
        tree = self.parser.ts_parser.parse(source_code.encode('utf-8'))
        exports = self.parser._extract_exports_from_tree(tree, source_code)

        # Should find multiple exports
        assert len(exports) >= 2
        assert "helper" in exports or "Component" in exports

    def test_process_import_statement_named(self):
        """Test processing named import statements."""
        # This would require mocking tree-sitter nodes
        # For now, we'll test the logic indirectly through integration tests
        pass

    def test_process_import_statement_default(self):
        """Test processing default import statements."""
        # This would require mocking tree-sitter nodes
        # For now, we'll test the logic indirectly through integration tests
        pass

    def test_process_require_statement(self):
        """Test processing CommonJS require statements."""
        # This would require mocking tree-sitter nodes
        # For now, we'll test the logic indirectly through integration tests
        pass

    def test_get_parser_for_file_extension(self):
        """Test getting the correct parser based on file extension."""
        # Test TypeScript files
        parser = self.parser._get_parser_for_file("/test/file.ts")
        assert parser == self.parser.ts_parser

        # Test TSX files
        parser = self.parser._get_parser_for_file("/test/file.tsx")
        assert parser == self.parser.tsx_parser

        # Test JavaScript files (should use TS parser)
        parser = self.parser._get_parser_for_file("/test/file.js")
        assert parser == self.parser.ts_parser

        # Test JSX files
        parser = self.parser._get_parser_for_file("/test/file.jsx")
        assert parser == self.parser.tsx_parser

    def test_node_to_attributes(self):
        """Test converting DependencyNode to graph attributes."""
        node = DependencyNode(
            file_path="/test/src/App.vue",
            imports=[],
            exports=["App"],
            is_entry_point=True,
            is_leaf_node=False
        )

        attrs = self.parser._node_to_attributes(node)

        assert attrs["is_entry_point"] is True
        assert attrs["is_leaf_node"] is False
        assert attrs["exports"] == ["App"]
        assert attrs["import_count"] == 0


class TestBaseParserIntegration:
    """Integration tests for BaseParser with real file parsing."""

    def setup_method(self):
        """Set up test fixtures."""
        self.parser = BaseParser()

    def test_parse_typescript_file(self):
        """Test parsing a real TypeScript file."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.ts', delete=False) as f:
            f.write('''
import { createApp } from 'vue';
import type { User } from './types/user';
import Button from './components/Button.vue';

export const app = createApp({});
export default app;
''')
            f.flush()

            try:
                # Parse the file
                tree = self.parser.ts_parser.parse(open(f.name, 'rb').read())
                content = open(f.name, 'r').read()

                imports = self.parser._extract_imports_from_tree(tree, content, f.name)
                exports = self.parser._extract_exports_from_tree(tree, content)

                # Verify imports were found
                assert len(imports) >= 2

                # Verify exports were found
                assert len(exports) >= 1

            finally:
                os.unlink(f.name)

    def test_parse_jsx_file(self):
        """Test parsing a real JSX file."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.jsx', delete=False) as f:
            f.write('''
import React from 'react';
import { Button } from './Button';

export const App = () => {
  return <div><Button /></div>;
};

export default App;
''')
            f.flush()

            try:
                # Parse the file
                tree = self.parser.tsx_parser.parse(open(f.name, 'rb').read())
                content = open(f.name, 'r').read()

                imports = self.parser._extract_imports_from_tree(tree, content, f.name)
                exports = self.parser._extract_exports_from_tree(tree, content)

                # Verify imports were found
                assert len(imports) >= 1

                # Verify exports were found
                assert len(exports) >= 1

            finally:
                os.unlink(f.name)
