# Developer Guide

## Architecture Overview

The Vue.js and React Dependency Analyzer is built with a modular architecture that separates concerns and allows for easy extension. The system consists of several key components:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   CLI Interface │    │  Main Analysis  │    │ Output Formats  │
│    (main.py)    │───▶│ (dependency_    │───▶│ (output_        │
│                 │    │  graph.py)      │    │  formatters.py) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │   File Parsers  │
                       │ ┌─────────────┐ │
                       │ │ BaseParser  │ │
                       │ └─────────────┘ │
                       │ ┌─────────────┐ │
                       │ │ VueParser   │ │
                       │ └─────────────┘ │
                       │ ┌─────────────┐ │
                       │ │ ReactParser │ │
                       │ └─────────────┘ │
                       └─────────────────┘
```

### Core Components

1. **BaseParser**: Abstract base class providing common parsing functionality
2. **VueParser**: Specialized parser for Vue Single File Components
3. **ReactParser**: Specialized parser for React/TypeScript files
4. **DependencyGraph**: Main analysis engine and graph management
5. **OutputFormatters**: Multiple output format implementations

## Extending the System

### Adding a New File Parser

To add support for a new framework or file type:

1. **Create a new parser class** inheriting from `BaseParser`:

```python
from dependency_analyzer import BaseParser, DependencyNode, ImportInfo

class AngularParser(BaseParser):
    """Parser for Angular components and services."""
    
    def __init__(self):
        super().__init__()
        # Add Angular-specific patterns
        self.angular_decorators = ['@Component', '@Injectable', '@Directive']
    
    def parse_file(self, file_path: str) -> Optional[DependencyNode]:
        """Parse Angular TypeScript files."""
        try:
            content = self._read_file_content(file_path)
            
            # Use TypeScript parser for Angular files
            tree = self.ts_parser.parse(content.encode('utf-8'))
            
            # Extract standard imports
            imports = self._extract_imports_from_tree(tree, content, file_path)
            
            # Add Angular-specific import detection
            angular_imports = self._extract_angular_imports(content, file_path)
            imports.extend(angular_imports)
            
            # Extract exports
            exports = self._extract_exports_from_tree(tree, content)
            
            return DependencyNode(
                file_path=file_path,
                imports=imports,
                exports=exports,
                is_entry_point=self._is_angular_entry_point(file_path),
                is_leaf_node=len(imports) == 0
            )
        except Exception as e:
            print(f"Error parsing Angular file {file_path}: {e}")
            return None
    
    def _extract_angular_imports(self, content: str, source_file: str) -> List[ImportInfo]:
        """Extract Angular-specific imports like template and style URLs."""
        imports = []
        
        # Look for templateUrl and styleUrls in @Component decorator
        template_pattern = r'templateUrl\s*:\s*[\'"]([^\'"]+)[\'"]'
        style_pattern = r'styleUrls\s*:\s*\[([^\]]+)\]'
        
        # Extract template dependencies
        for match in re.finditer(template_pattern, content):
            template_path = match.group(1)
            resolved_path, dep_type = self._resolve_import_path(f"'{template_path}'", source_file)
            
            imports.append(ImportInfo(
                source_file=source_file,
                imported_module=resolved_path,
                import_type=ImportType.DEFAULT,
                dependency_type=dep_type,
                imported_names=[],
                line_number=self._get_line_number(content, match.start())
            ))
        
        return imports
    
    def _is_angular_entry_point(self, file_path: str) -> bool:
        """Determine if file is an Angular entry point."""
        filename = Path(file_path).name
        return filename in ['main.ts', 'app.module.ts', 'app.component.ts']
```

2. **Register the parser** in `DependencyGraph`:

```python
class DependencyGraph:
    def __init__(self, project_root: str):
        # ... existing code ...
        self.angular_parser = AngularParser()
        self.supported_extensions.add('.ts')  # If not already added
    
    def _parse_file(self, file_path: str) -> Optional[DependencyNode]:
        ext = Path(file_path).suffix.lower()
        
        if ext == '.vue':
            return self.vue_parser.parse_file(file_path)
        elif ext in ['.tsx', '.jsx']:
            return self.react_parser.parse_file(file_path)
        elif ext in ['.ts', '.js']:
            # Check if it's an Angular file
            if self._is_angular_file(file_path):
                return self.angular_parser.parse_file(file_path)
            else:
                return self.react_parser.parse_file(file_path)
        
        return None
    
    def _is_angular_file(self, file_path: str) -> bool:
        """Determine if a TypeScript file is Angular-specific."""
        content = self.angular_parser._read_file_content(file_path)
        return any(decorator in content for decorator in ['@Component', '@Injectable', '@NgModule'])
```

### Adding a New Output Format

To add a new output format:

1. **Create a formatter class** inheriting from `OutputFormatter`:

```python
from output_formatters import OutputFormatter

class PlantUMLFormatter(OutputFormatter):
    """Format dependency graph as PlantUML diagram."""
    
    def format(self, include_packages: bool = True) -> str:
        """Generate PlantUML syntax."""
        lines = ['@startuml']
        lines.append('!define COMPONENT_STYLE class')
        lines.append('')
        
        # Add components
        for file_path in self.graph.nodes:
            label = self._get_file_label(file_path)
            component_name = label.replace('/', '_').replace('.', '_')
            lines.append(f'COMPONENT_STYLE {component_name} as "{label}"')
        
        lines.append('')
        
        # Add relationships
        for source, target in self.graph.graph.edges():
            source_name = self._get_file_label(source).replace('/', '_').replace('.', '_')
            target_name = self._get_file_label(target).replace('/', '_').replace('.', '_')
            lines.append(f'{source_name} --> {target_name}')
        
        lines.append('@enduml')
        return '\n'.join(lines)
```

2. **Register the formatter** in the CLI or main module:

```python
from output_formatters import PlantUMLFormatter

# In main.py or wherever formatters are used
def _generate_output(dependency_graph, format_type, **options):
    if format_type == 'json':
        formatter = JSONFormatter(dependency_graph)
        return formatter.format(options.get('include_details', True))
    elif format_type == 'plantuml':
        formatter = PlantUMLFormatter(dependency_graph)
        return formatter.format(options.get('include_packages', True))
    # ... other formats
```

### Customizing Analysis Behavior

#### Custom File Detection

Override file detection logic:

```python
class CustomDependencyGraph(DependencyGraph):
    def _find_files_to_analyze(self, exclude_patterns: List[str]) -> List[str]:
        """Custom file discovery logic."""
        files = super()._find_files_to_analyze(exclude_patterns)
        
        # Add custom filtering
        custom_files = []
        for file_path in files:
            # Skip test files
            if 'test' not in file_path.lower() and 'spec' not in file_path.lower():
                custom_files.append(file_path)
        
        return custom_files
```

#### Custom Import Resolution

Override import path resolution:

```python
class CustomBaseParser(BaseParser):
    def _resolve_import_path(self, import_path: str, source_file: str) -> Tuple[str, DependencyType]:
        """Custom import resolution with project-specific aliases."""
        clean_path = import_path.strip('\'"')
        
        # Handle custom aliases
        if clean_path.startswith('@shared/'):
            # Resolve @shared/ to shared library path
            resolved = clean_path.replace('@shared/', '/path/to/shared/lib/')
            return resolved, DependencyType.LOCAL
        elif clean_path.startswith('@api/'):
            # Resolve @api/ to API layer
            resolved = clean_path.replace('@api/', '/path/to/api/')
            return resolved, DependencyType.LOCAL
        
        # Fall back to default resolution
        return super()._resolve_import_path(import_path, source_file)
```

## Testing Extensions

When adding new functionality, ensure comprehensive testing:

```python
# tests/test_angular_parser.py
import pytest
from your_extension import AngularParser

class TestAngularParser:
    def setup_method(self):
        self.parser = AngularParser()
    
    def test_parse_component_file(self):
        """Test parsing Angular component files."""
        # Create test file content
        content = '''
        import { Component } from '@angular/core';
        
        @Component({
          selector: 'app-user',
          templateUrl: './user.component.html',
          styleUrls: ['./user.component.css']
        })
        export class UserComponent {
          // component logic
        }
        '''
        
        # Test parsing logic
        # ... test implementation
    
    def test_angular_specific_imports(self):
        """Test extraction of Angular-specific imports."""
        # ... test implementation
```

## Performance Optimization

### Caching Strategies

Implement caching for large projects:

```python
import pickle
from pathlib import Path

class CachedDependencyGraph(DependencyGraph):
    def __init__(self, project_root: str, cache_dir: str = None):
        super().__init__(project_root)
        self.cache_dir = Path(cache_dir or project_root) / '.dependency_cache'
        self.cache_dir.mkdir(exist_ok=True)
    
    def analyze_project(self, exclude_patterns: Optional[List[str]] = None) -> None:
        """Analyze project with caching support."""
        cache_file = self.cache_dir / 'dependency_graph.pkl'
        
        # Check if cache is valid
        if self._is_cache_valid(cache_file):
            self._load_from_cache(cache_file)
            return
        
        # Perform analysis
        super().analyze_project(exclude_patterns)
        
        # Save to cache
        self._save_to_cache(cache_file)
    
    def _is_cache_valid(self, cache_file: Path) -> bool:
        """Check if cache is still valid."""
        if not cache_file.exists():
            return False
        
        cache_time = cache_file.stat().st_mtime
        
        # Check if any source files are newer than cache
        for file_path in self._find_files_to_analyze([]):
            if Path(file_path).stat().st_mtime > cache_time:
                return False
        
        return True
```

### Parallel Processing

Add parallel processing for large projects:

```python
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Optional

class ParallelDependencyGraph(DependencyGraph):
    def analyze_project(self, exclude_patterns: Optional[List[str]] = None, max_workers: int = 4) -> None:
        """Analyze project with parallel file processing."""
        if exclude_patterns is None:
            exclude_patterns = ['node_modules', '.git', 'dist', 'build', '.next', '.nuxt']
        
        files_to_analyze = self._find_files_to_analyze(exclude_patterns)
        
        # Process files in parallel
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_file = {
                executor.submit(self._parse_file, file_path): file_path 
                for file_path in files_to_analyze
            }
            
            for future in as_completed(future_to_file):
                file_path = future_to_file[future]
                try:
                    node = future.result()
                    if node:
                        self.nodes[file_path] = node
                        self.graph.add_node(file_path, **self._node_to_attributes(node))
                except Exception as e:
                    print(f"Error processing {file_path}: {e}")
        
        # Build edges and analyze properties
        self._build_graph_edges()
        self._analyze_graph_properties()
```

## Contributing Guidelines

1. **Follow the existing architecture patterns**
2. **Add comprehensive tests for new functionality**
3. **Update documentation for API changes**
4. **Use type hints consistently**
5. **Follow PEP 8 style guidelines**
6. **Add docstrings to all public methods**
7. **Handle errors gracefully**
8. **Consider performance implications**

## Debugging Tips

### Enable Verbose Logging

```python
import logging

logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# Add logging to your extensions
logger.debug(f"Processing file: {file_path}")
logger.info(f"Found {len(imports)} imports")
```

### Inspect Parse Trees

```python
# Debug tree-sitter parsing
tree = parser.parse(content.encode('utf-8'))
print(tree.root_node.sexp())  # S-expression representation
```

### Visualize Graphs

```python
import matplotlib.pyplot as plt
import networkx as nx

# Visualize the dependency graph
pos = nx.spring_layout(graph.graph)
nx.draw(graph.graph, pos, with_labels=True, node_color='lightblue')
plt.show()
```
