"""
Unit tests for the Vue.js parser module.

Tests the VueParser class and its ability to parse Vue Single File Components,
extract script sections, analyze template component usage, and detect Vue-specific imports.
"""

import pytest
from pathlib import Path
from unittest.mock import Mock, patch, mock_open
import tempfile
import os

from vue_parser import VueParser
from dependency_analyzer import DependencyNode, ImportInfo, ImportType, DependencyType


class TestVueParser:
    """Test the VueParser class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.parser = VueParser()
    
    def test_vue_parser_initialization(self):
        """Test VueParser initialization."""
        assert self.parser.ts_language is not None
        assert self.parser.tsx_language is not None
        assert self.parser.ts_parser is not None
        assert self.parser.tsx_parser is not None
    
    @patch('vue_parser.VueParser._read_file_content')
    def test_parse_file_success(self, mock_read):
        """Test successful parsing of a Vue file."""
        mock_read.return_value = '''
<template>
  <div>
    <UserCard :user="user" />
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
import UserCard from './UserCard.vue'

export default defineComponent({
  name: 'UserProfile',
  components: {
    UserCard
  }
})
</script>
'''
        
        result = self.parser.parse_file("/test/src/UserProfile.vue")
        
        assert result is not None
        assert isinstance(result, DependencyNode)
        assert result.file_path == "/test/src/UserProfile.vue"
        assert len(result.imports) >= 1
        assert any(imp.imported_module == "vue" for imp in result.imports)
    
    @patch('vue_parser.VueParser._read_file_content')
    def test_parse_file_error_handling(self, mock_read):
        """Test error handling during Vue file parsing."""
        mock_read.side_effect = Exception("File read error")
        
        result = self.parser.parse_file("/test/src/UserProfile.vue")
        
        assert result is None
    
    def test_extract_script_sections_typescript(self):
        """Test extracting TypeScript script sections."""
        content = '''
<template>
  <div>Hello</div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
export default defineComponent({})
</script>

<style scoped>
.hello { color: blue; }
</style>
'''
        
        sections = self.parser._extract_script_sections(content)
        
        assert len(sections) == 1
        script_content, script_lang = sections[0]
        assert "defineComponent" in script_content
        assert script_lang == "ts"
    
    def test_extract_script_sections_javascript(self):
        """Test extracting JavaScript script sections."""
        content = '''
<template>
  <div>Hello</div>
</template>

<script>
export default {
  name: 'Component'
}
</script>
'''
        
        sections = self.parser._extract_script_sections(content)
        
        assert len(sections) == 1
        script_content, script_lang = sections[0]
        assert "name: 'Component'" in script_content
        assert script_lang == "js"
    
    def test_extract_script_sections_setup(self):
        """Test extracting script setup sections."""
        content = '''
<template>
  <div>{{ message }}</div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
const message = ref('Hello')
</script>
'''
        
        sections = self.parser._extract_script_sections(content)
        
        assert len(sections) == 1
        script_content, script_lang = sections[0]
        assert "ref" in script_content
        assert script_lang == "ts"
    
    def test_extract_script_sections_multiple(self):
        """Test extracting multiple script sections."""
        content = '''
<template>
  <div>Hello</div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
export default defineComponent({})
</script>

<script setup lang="ts">
import { ref } from 'vue'
const count = ref(0)
</script>
'''
        
        sections = self.parser._extract_script_sections(content)
        
        assert len(sections) == 2
        assert any("defineComponent" in content for content, _ in sections)
        assert any("ref" in content for content, _ in sections)
    
    def test_extract_script_sections_no_lang(self):
        """Test extracting script sections without lang attribute."""
        content = '''
<script>
export default {
  name: 'Component'
}
</script>
'''
        
        sections = self.parser._extract_script_sections(content)
        
        assert len(sections) == 1
        script_content, script_lang = sections[0]
        assert script_lang == "js"  # Default to JavaScript
    
    def test_get_parser_for_script_typescript(self):
        """Test getting TypeScript parser for script."""
        parser = self.parser._get_parser_for_script("ts")
        assert parser == self.parser.ts_parser
        
        parser = self.parser._get_parser_for_script("typescript")
        assert parser == self.parser.ts_parser
    
    def test_get_parser_for_script_jsx(self):
        """Test getting JSX parser for script."""
        parser = self.parser._get_parser_for_script("tsx")
        assert parser == self.parser.tsx_parser
        
        parser = self.parser._get_parser_for_script("jsx")
        assert parser == self.parser.tsx_parser
    
    def test_get_parser_for_script_default(self):
        """Test getting default parser for script."""
        parser = self.parser._get_parser_for_script("js")
        assert parser == self.parser.ts_parser
        
        parser = self.parser._get_parser_for_script("javascript")
        assert parser == self.parser.ts_parser
        
        parser = self.parser._get_parser_for_script("")
        assert parser == self.parser.ts_parser
    
    def test_extract_vue_component_imports(self):
        """Test extracting Vue component imports from script content."""
        script_content = '''
import UserCard from './UserCard.vue'
import ContactInfo from '../common/ContactInfo.vue'

export default defineComponent({
  components: {
    UserCard,
    ContactInfo,
    'custom-button': Button
  }
})
'''
        
        imports = self.parser._extract_vue_component_imports(script_content, "/test/src/UserProfile.vue")
        
        # Should find component registrations
        assert len(imports) >= 0  # May vary based on implementation
    
    def test_extract_template_component_usage(self):
        """Test extracting component usage from template section."""
        content = '''
<template>
  <div class="user-profile">
    <UserCard :user="user" />
    <ContactInfo :contact="user.contact" />
    <custom-button @click="handleClick" />
    <el-button type="primary">Submit</el-button>
  </div>
</template>

<script>
// script content
</script>
'''
        
        imports = self.parser._extract_template_component_usage(content, "/test/src/UserProfile.vue")
        
        # Should find component usage in template
        assert len(imports) >= 0  # May vary based on implementation
    
    def test_extract_component_name(self):
        """Test extracting component name from Vue file."""
        content = '''
<script>
export default {
  name: 'UserProfile'
}
</script>
'''
        
        name = self.parser._extract_component_name(content)
        assert name == "UserProfile"
    
    def test_extract_component_name_define_component(self):
        """Test extracting component name from defineComponent."""
        content = '''
<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'ContactInfo'
})
</script>
'''
        
        name = self.parser._extract_component_name(content)
        assert name == "ContactInfo"
    
    def test_extract_component_name_not_found(self):
        """Test extracting component name when not found."""
        content = '''
<script>
export default {}
</script>
'''
        
        name = self.parser._extract_component_name(content)
        assert name is None
    
    def test_extract_props(self):
        """Test extracting props from Vue component."""
        content = '''
<script>
export default {
  props: {
    user: {
      type: Object,
      required: true
    },
    title: String,
    count: Number
  }
}
</script>
'''
        
        props = self.parser._extract_props(content)
        assert "user" in props
        assert "title" in props
        assert "count" in props
    
    def test_extract_computed_properties(self):
        """Test extracting computed properties from Vue component."""
        content = '''
<script>
export default {
  computed: {
    fullName() {
      return this.firstName + ' ' + this.lastName
    },
    displayName: {
      get() { return this.name },
      set(value) { this.name = value }
    }
  }
}
</script>
'''
        
        computed = self.parser._extract_computed_properties(content)
        assert "fullName" in computed
        assert "displayName" in computed
    
    def test_extract_methods(self):
        """Test extracting methods from Vue component."""
        content = '''
<script>
export default {
  methods: {
    handleClick() {
      console.log('clicked')
    },
    async fetchData() {
      return await api.getData()
    }
  }
}
</script>
'''
        
        methods = self.parser._extract_methods(content)
        assert "handleClick" in methods
        assert "fetchData" in methods

    def test_get_vue_specific_info(self):
        """Test extracting Vue-specific information from a file."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.vue', delete=False) as f:
            f.write('''
<template>
  <div class="user-profile">
    <h1>{{ title }}</h1>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed } from 'vue'

export default defineComponent({
  name: 'UserProfile',
  props: {
    userId: String
  },
  setup(props) {
    const title = ref('User Profile')
    const displayTitle = computed(() => `Profile: ${title.value}`)

    return {
      title,
      displayTitle
    }
  }
})
</script>

<style scoped>
.user-profile {
  padding: 20px;
}
</style>
''')
            f.flush()

            try:
                vue_info = self.parser.get_vue_specific_info(f.name)

                assert vue_info['has_template'] is True
                assert vue_info['has_style'] is True
                assert 'ts' in vue_info['script_languages']
                assert vue_info['component_name'] == 'UserProfile'

            finally:
                os.unlink(f.name)


class TestVueParserIntegration:
    """Integration tests for VueParser with real Vue files."""

    def setup_method(self):
        """Set up test fixtures."""
        self.parser = VueParser()

    def test_parse_complete_vue_file(self):
        """Test parsing a complete Vue Single File Component."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.vue', delete=False) as f:
            f.write('''
<template>
  <div class="user-profile">
    <UserCard :user="user" @click="handleUserClick" />
    <ContactInfo :contact="user.contact" />
    <ActionButton @click="handleAction">Save</ActionButton>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, onMounted } from 'vue'
import UserCard from './UserCard.vue'
import ContactInfo from '../common/ContactInfo.vue'
import ActionButton from '@/components/ActionButton.vue'
import { User } from '@/types/user'
import { userService } from '@/services/userService'

export default defineComponent({
  name: 'UserProfile',
  components: {
    UserCard,
    ContactInfo,
    ActionButton
  },
  props: {
    userId: {
      type: String,
      required: true
    }
  },
  setup(props) {
    const user = ref<User | null>(null)
    const loading = ref(true)

    const displayName = computed(() => {
      return user.value ? `${user.value.firstName} ${user.value.lastName}` : ''
    })

    const handleUserClick = () => {
      console.log('User clicked')
    }

    const handleAction = async () => {
      if (user.value) {
        await userService.updateUser(user.value)
      }
    }

    onMounted(async () => {
      try {
        user.value = await userService.getUser(props.userId)
      } finally {
        loading.value = false
      }
    })

    return {
      user,
      loading,
      displayName,
      handleUserClick,
      handleAction
    }
  }
})
</script>

<style scoped>
.user-profile {
  padding: 20px;
  background: white;
  border-radius: 8px;
}
</style>
''')
            f.flush()

            try:
                result = self.parser.parse_file(f.name)

                assert result is not None
                assert isinstance(result, DependencyNode)
                assert result.file_path == f.name

                # Check that imports were found
                assert len(result.imports) > 0

                # Check for Vue import
                vue_import = next((imp for imp in result.imports if imp.imported_module == "vue"), None)
                assert vue_import is not None
                assert vue_import.import_type == ImportType.NAMED
                assert "defineComponent" in vue_import.imported_names

                # Check for local component imports
                local_imports = [imp for imp in result.imports if imp.dependency_type == DependencyType.LOCAL]
                assert len(local_imports) > 0

                # Check exports
                assert len(result.exports) > 0

            finally:
                os.unlink(f.name)

    def test_parse_vue_file_with_script_setup(self):
        """Test parsing Vue file with script setup syntax."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.vue', delete=False) as f:
            f.write('''
<template>
  <div>
    <h1>{{ title }}</h1>
    <UserCard :user="user" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import UserCard from './UserCard.vue'
import type { User } from '@/types/user'

const title = ref('Profile')
const user = ref<User | null>(null)

const displayTitle = computed(() => `User ${title.value}`)

defineExpose({
  title,
  user
})
</script>
''')
            f.flush()

            try:
                result = self.parser.parse_file(f.name)

                assert result is not None
                assert isinstance(result, DependencyNode)

                # Check that imports were found
                assert len(result.imports) > 0

                # Check for Vue import
                vue_import = next((imp for imp in result.imports if imp.imported_module == "vue"), None)
                assert vue_import is not None

                # Check for type-only import
                type_imports = [imp for imp in result.imports if imp.is_type_only]
                assert len(type_imports) >= 0  # May or may not be detected depending on implementation

            finally:
                os.unlink(f.name)

    def test_parse_vue_file_composition_api(self):
        """Test parsing Vue file using Composition API."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.vue', delete=False) as f:
            f.write('''
<template>
  <div>
    <p>{{ message }}</p>
    <button @click="increment">Count: {{ count }}</button>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, reactive, computed, watch } from 'vue'

interface State {
  count: number
  message: string
}

export default defineComponent({
  setup() {
    const count = ref(0)
    const state = reactive<State>({
      count: 0,
      message: 'Hello'
    })

    const doubleCount = computed(() => count.value * 2)

    watch(count, (newVal) => {
      console.log('Count changed:', newVal)
    })

    const increment = () => {
      count.value++
      state.count++
    }

    return {
      count,
      state,
      doubleCount,
      increment
    }
  }
})
</script>
''')
            f.flush()

            try:
                result = self.parser.parse_file(f.name)

                assert result is not None
                assert isinstance(result, DependencyNode)

                # Check that Vue imports were found
                vue_import = next((imp for imp in result.imports if imp.imported_module == "vue"), None)
                assert vue_import is not None
                assert "defineComponent" in vue_import.imported_names
                assert "ref" in vue_import.imported_names
                assert "reactive" in vue_import.imported_names

            finally:
                os.unlink(f.name)
