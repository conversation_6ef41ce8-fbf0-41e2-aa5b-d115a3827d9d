"""
Unit tests for the React parser module.

Tests the ReactParser class and its ability to parse React components,
extract JSX usage, analyze hook usage, and detect TypeScript patterns.
"""

import pytest
from pathlib import Path
from unittest.mock import Mock, patch, mock_open
import tempfile
import os

from react_parser import ReactParser
from dependency_analyzer import DependencyNode, ImportInfo, ImportType, DependencyType


class TestReactParser:
    """Test the ReactParser class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.parser = ReactParser()
    
    def test_react_parser_initialization(self):
        """Test ReactParser initialization."""
        assert self.parser.ts_language is not None
        assert self.parser.tsx_language is not None
        assert self.parser.ts_parser is not None
        assert self.parser.tsx_parser is not None
        
        # Check React-specific attributes
        assert len(self.parser.react_hooks) > 0
        assert 'useState' in self.parser.react_hooks
        assert 'useEffect' in self.parser.react_hooks
        assert len(self.parser.react_component_patterns) > 0
    
    @patch('react_parser.ReactParser._read_file_content')
    def test_parse_file_success(self, mock_read):
        """Test successful parsing of a React file."""
        mock_read.return_value = '''
import React, { useState } from 'react';
import { Button } from './Button';

export const UserProfile = ({ userId }) => {
  const [user, setUser] = useState(null);
  
  return (
    <div>
      <h1>User Profile</h1>
      <Button onClick={() => console.log('clicked')} />
    </div>
  );
};

export default UserProfile;
'''
        
        result = self.parser.parse_file("/test/src/UserProfile.tsx")
        
        assert result is not None
        assert isinstance(result, DependencyNode)
        assert result.file_path == "/test/src/UserProfile.tsx"
        assert len(result.imports) >= 1
        assert any(imp.imported_module == "react" for imp in result.imports)
    
    @patch('react_parser.ReactParser._read_file_content')
    def test_parse_file_error_handling(self, mock_read):
        """Test error handling during React file parsing."""
        mock_read.side_effect = Exception("File read error")
        
        result = self.parser.parse_file("/test/src/UserProfile.tsx")
        
        assert result is None
    
    def test_get_parser_for_file_tsx(self):
        """Test getting TSX parser for .tsx files."""
        parser = self.parser._get_parser_for_file("/test/Component.tsx")
        assert parser == self.parser.tsx_parser
    
    def test_get_parser_for_file_jsx(self):
        """Test getting TSX parser for .jsx files."""
        parser = self.parser._get_parser_for_file("/test/Component.jsx")
        assert parser == self.parser.tsx_parser
    
    def test_get_parser_for_file_ts(self):
        """Test getting TS parser for .ts files."""
        parser = self.parser._get_parser_for_file("/test/utils.ts")
        assert parser == self.parser.ts_parser
    
    def test_get_parser_for_file_js(self):
        """Test getting TS parser for .js files."""
        parser = self.parser._get_parser_for_file("/test/utils.js")
        assert parser == self.parser.ts_parser
    
    def test_extract_react_specific_imports(self):
        """Test extracting React-specific imports."""
        content = '''
import React, { Component } from 'react';
import { createContext } from 'react';
import { render } from 'react-dom';
'''
        
        imports = self.parser._extract_react_specific_imports(content, "/test/src/App.tsx")
        
        # Should find React-related imports
        assert len(imports) >= 0  # May vary based on implementation
    
    def test_extract_jsx_component_usage(self):
        """Test extracting JSX component usage."""
        content = '''
const App = () => {
  return (
    <div>
      <UserProfile userId="123" />
      <Button type="primary">Click me</Button>
      <CustomComponent data={data} />
    </div>
  );
};
'''
        
        # Parse with TSX parser
        tree = self.parser.tsx_parser.parse(content.encode('utf-8'))
        imports = self.parser._extract_jsx_component_usage(tree, content, "/test/src/App.tsx")
        
        # Should find JSX component usage
        assert len(imports) >= 0  # May vary based on implementation
    
    def test_extract_react_components(self):
        """Test extracting React component definitions."""
        content = '''
function UserProfile(props) {
  return <div>Profile</div>;
}

const Button = (props) => {
  return <button>{props.children}</button>;
};

class Modal extends React.Component {
  render() {
    return <div>Modal</div>;
  }
}

export { UserProfile, Button };
export default Modal;
'''
        
        components = self.parser._extract_react_components(content)
        
        assert "UserProfile" in components
        assert "Button" in components
        assert "Modal" in components
    
    def test_uses_react_hooks(self):
        """Test detecting React hooks usage."""
        content_with_hooks = '''
import { useState, useEffect } from 'react';

const Component = () => {
  const [state, setState] = useState(null);
  
  useEffect(() => {
    // effect
  }, []);
  
  return <div></div>;
};
'''
        
        content_without_hooks = '''
import React from 'react';

class Component extends React.Component {
  render() {
    return <div></div>;
  }
}
'''
        
        assert self.parser._uses_react_hooks(content_with_hooks) is True
        assert self.parser._uses_react_hooks(content_without_hooks) is False
    
    def test_get_hooks_used(self):
        """Test getting list of hooks used in component."""
        content = '''
import { useState, useEffect, useCallback, useMemo } from 'react';

const Component = () => {
  const [state, setState] = useState(null);
  const [count, setCount] = useState(0);
  
  useEffect(() => {
    // effect
  }, []);
  
  const memoizedValue = useMemo(() => {
    return count * 2;
  }, [count]);
  
  const handleClick = useCallback(() => {
    setState(prev => !prev);
  }, []);
  
  return <div></div>;
};
'''
        
        hooks = self.parser._get_hooks_used(content)
        
        assert 'useState' in hooks
        assert 'useEffect' in hooks
        assert 'useCallback' in hooks
        assert 'useMemo' in hooks
    
    def test_get_component_type(self):
        """Test determining component type."""
        function_component = '''
function UserProfile() {
  return <div>Profile</div>;
}
'''
        
        arrow_component = '''
const Button = () => {
  return <button>Click</button>;
};
'''
        
        class_component = '''
class Modal extends React.Component {
  render() {
    return <div>Modal</div>;
  }
}
'''
        
        assert self.parser._get_component_type(function_component) == "function"
        assert self.parser._get_component_type(arrow_component) == "function"
        assert self.parser._get_component_type(class_component) == "class"
    
    def test_has_jsx(self):
        """Test detecting JSX usage."""
        jsx_content = '''
const Component = () => {
  return <div><span>Hello</span></div>;
};
'''
        
        no_jsx_content = '''
const utils = {
  helper: () => {
    return "hello";
  }
};
'''
        
        assert self.parser._has_jsx(jsx_content) is True
        assert self.parser._has_jsx(no_jsx_content) is False
    
    def test_extract_prop_types(self):
        """Test extracting PropTypes definitions."""
        content = '''
import PropTypes from 'prop-types';

const Component = ({ name, age, isActive }) => {
  return <div>{name}</div>;
};

Component.propTypes = {
  name: PropTypes.string.isRequired,
  age: PropTypes.number,
  isActive: PropTypes.bool
};
'''
        
        prop_types = self.parser._extract_prop_types(content)
        
        assert 'name' in prop_types
        assert 'age' in prop_types
        assert 'isActive' in prop_types
    
    def test_has_state_usage(self):
        """Test detecting state usage in components."""
        hooks_state = '''
const Component = () => {
  const [state, setState] = useState(null);
  return <div></div>;
};
'''
        
        class_state = '''
class Component extends React.Component {
  constructor(props) {
    super(props);
    this.state = { count: 0 };
  }
  
  render() {
    return <div>{this.state.count}</div>;
  }
}
'''
        
        no_state = '''
const Component = (props) => {
  return <div>{props.message}</div>;
};
'''
        
        assert self.parser._has_state_usage(hooks_state) is True
        assert self.parser._has_state_usage(class_state) is True
        assert self.parser._has_state_usage(no_state) is False

    def test_get_react_specific_info(self):
        """Test extracting React-specific information from a file."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.tsx', delete=False) as f:
            f.write('''
import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';

interface UserProfileProps {
  userId: string;
  onUserLoad?: (user: any) => void;
}

export const UserProfile: React.FC<UserProfileProps> = ({ userId, onUserLoad }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Fetch user data
  }, [userId]);

  return (
    <div className="user-profile">
      <h1>User Profile</h1>
    </div>
  );
};

UserProfile.propTypes = {
  userId: PropTypes.string.isRequired,
  onUserLoad: PropTypes.func
};

export default UserProfile;
''')
            f.flush()

            try:
                react_info = self.parser.get_react_specific_info(f.name)

                assert react_info['uses_hooks'] is True
                assert 'useState' in react_info['hooks_used']
                assert 'useEffect' in react_info['hooks_used']
                assert react_info['component_type'] == 'function'
                assert react_info['has_jsx'] is True
                assert react_info['uses_typescript'] is True
                assert react_info['state_usage'] is True

            finally:
                os.unlink(f.name)


class TestReactParserIntegration:
    """Integration tests for ReactParser with real React files."""

    def setup_method(self):
        """Set up test fixtures."""
        self.parser = ReactParser()

    def test_parse_complete_react_component(self):
        """Test parsing a complete React component with TypeScript."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.tsx', delete=False) as f:
            f.write('''
import React, { useState, useEffect, useCallback } from 'react';
import { UserCard } from './UserCard';
import { ContactInfo } from '../common/ContactInfo';
import { ActionButton } from '@/components/ActionButton';
import { User } from '../types/user';
import { userService } from '../services/userService';

interface UserProfileProps {
  userId: string;
  onUserUpdate?: (user: User) => void;
}

export const UserProfile: React.FC<UserProfileProps> = ({
  userId,
  onUserUpdate
}) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchUser = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const userData = await userService.getUser(userId);
      setUser(userData);
      onUserUpdate?.(userData);
    } catch (err) {
      setError('Failed to load user');
      console.error('Error fetching user:', err);
    } finally {
      setLoading(false);
    }
  }, [userId, onUserUpdate]);

  useEffect(() => {
    fetchUser();
  }, [fetchUser]);

  const handleUserClick = useCallback(() => {
    console.log('User clicked:', user?.id);
  }, [user]);

  const handleSave = useCallback(async () => {
    if (!user) return;

    try {
      await userService.updateUser(user);
      onUserUpdate?.(user);
    } catch (err) {
      setError('Failed to save user');
    }
  }, [user, onUserUpdate]);

  if (loading) {
    return <div className="loading">Loading user...</div>;
  }

  if (error) {
    return <div className="error">{error}</div>;
  }

  if (!user) {
    return <div className="not-found">User not found</div>;
  }

  return (
    <div className="user-profile">
      <UserCard
        user={user}
        onClick={handleUserClick}
        className="user-card"
      />
      <ContactInfo
        contact={user.contact}
        editable={true}
      />
      <ActionButton
        onClick={handleSave}
        disabled={loading}
        variant="primary"
      >
        Save Changes
      </ActionButton>
    </div>
  );
};

export default UserProfile;
''')
            f.flush()

            try:
                result = self.parser.parse_file(f.name)

                assert result is not None
                assert isinstance(result, DependencyNode)
                assert result.file_path == f.name

                # Check that imports were found
                assert len(result.imports) > 0

                # Check for React import
                react_import = next((imp for imp in result.imports if imp.imported_module == "react"), None)
                assert react_import is not None
                assert imp.import_type == ImportType.NAMED
                assert "useState" in react_import.imported_names
                assert "useEffect" in react_import.imported_names

                # Check for local component imports
                local_imports = [imp for imp in result.imports if imp.dependency_type == DependencyType.LOCAL]
                assert len(local_imports) > 0

                # Check exports
                assert len(result.exports) > 0
                assert "UserProfile" in result.exports

            finally:
                os.unlink(f.name)

    def test_parse_react_class_component(self):
        """Test parsing a React class component."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.jsx', delete=False) as f:
            f.write('''
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { Button } from './Button';

class UserProfile extends Component {
  constructor(props) {
    super(props);
    this.state = {
      user: null,
      loading: true,
      error: null
    };
  }

  componentDidMount() {
    this.fetchUser();
  }

  componentDidUpdate(prevProps) {
    if (prevProps.userId !== this.props.userId) {
      this.fetchUser();
    }
  }

  fetchUser = async () => {
    try {
      this.setState({ loading: true, error: null });
      const response = await fetch(`/api/users/${this.props.userId}`);
      const user = await response.json();
      this.setState({ user, loading: false });
    } catch (error) {
      this.setState({ error: error.message, loading: false });
    }
  };

  handleSave = () => {
    console.log('Saving user:', this.state.user);
  };

  render() {
    const { user, loading, error } = this.state;

    if (loading) {
      return <div>Loading...</div>;
    }

    if (error) {
      return <div>Error: {error}</div>;
    }

    return (
      <div className="user-profile">
        <h1>{user?.name}</h1>
        <Button onClick={this.handleSave}>
          Save
        </Button>
      </div>
    );
  }
}

UserProfile.propTypes = {
  userId: PropTypes.string.isRequired,
  onUserUpdate: PropTypes.func
};

UserProfile.defaultProps = {
  onUserUpdate: () => {}
};

export default UserProfile;
''')
            f.flush()

            try:
                result = self.parser.parse_file(f.name)

                assert result is not None
                assert isinstance(result, DependencyNode)

                # Check that imports were found
                assert len(result.imports) > 0

                # Check for React import
                react_import = next((imp for imp in result.imports if imp.imported_module == "react"), None)
                assert react_import is not None
                assert "Component" in react_import.imported_names

                # Check exports
                assert len(result.exports) > 0
                assert "UserProfile" in result.exports

            finally:
                os.unlink(f.name)

    def test_parse_react_hooks_only_file(self):
        """Test parsing a file with custom React hooks."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.ts', delete=False) as f:
            f.write('''
import { useState, useEffect, useCallback } from 'react';
import { userService } from '../services/userService';
import type { User } from '../types/user';

export const useUser = (userId: string) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchUser = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const userData = await userService.getUser(userId);
      setUser(userData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  }, [userId]);

  useEffect(() => {
    if (userId) {
      fetchUser();
    }
  }, [userId, fetchUser]);

  const updateUser = useCallback(async (updates: Partial<User>) => {
    if (!user) return;

    try {
      const updatedUser = await userService.updateUser({ ...user, ...updates });
      setUser(updatedUser);
      return updatedUser;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Update failed');
      throw err;
    }
  }, [user]);

  return {
    user,
    loading,
    error,
    refetch: fetchUser,
    updateUser
  };
};

export const useUserList = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchUsers = useCallback(async () => {
    setLoading(true);
    try {
      const userList = await userService.getUsers();
      setUsers(userList);
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    users,
    loading,
    fetchUsers
  };
};
''')
            f.flush()

            try:
                result = self.parser.parse_file(f.name)

                assert result is not None
                assert isinstance(result, DependencyNode)

                # Check that imports were found
                assert len(result.imports) > 0

                # Check for React import
                react_import = next((imp for imp in result.imports if imp.imported_module == "react"), None)
                assert react_import is not None
                assert "useState" in react_import.imported_names
                assert "useEffect" in react_import.imported_names
                assert "useCallback" in react_import.imported_names

                # Check exports (custom hooks)
                assert len(result.exports) > 0
                assert "useUser" in result.exports
                assert "useUserList" in result.exports

            finally:
                os.unlink(f.name)
