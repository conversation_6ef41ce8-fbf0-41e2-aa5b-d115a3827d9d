"""
Output Formatters for Dependency Analysis

This module provides multiple output formats for dependency analysis results,
enabling integration with various tools and workflows. Each formatter converts
the dependency graph into a specific format optimized for different use cases.

Available formatters:
- JSONFormatter: Structured data for programmatic processing
- DOTFormatter: Graphviz format for professional graph visualization
- MermaidFormatter: Interactive diagrams for documentation
- TextTreeFormatter: Human-readable hierarchical structure

Each formatter supports customization options and handles edge cases like
circular dependencies, external packages, and complex project structures.
"""

import json
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import asdict

from dependency_analyzer import DependencyNode, ImportInfo
from dependency_graph import DependencyGraph


class OutputFormatter:
    """
    Base class for all output formatters.

    Provides common functionality for converting dependency graphs into
    various output formats. Subclasses implement specific formatting logic
    while inheriting path resolution and labeling utilities.

    Attributes:
        graph (DependencyGraph): The dependency graph to format
        project_root (Path): Root directory of the analyzed project

    Examples:
        >>> # Subclass usage
        >>> class CustomFormatter(OutputFormatter):
        ...     def format(self):
        ...         return "Custom format output"
    """

    def __init__(self, dependency_graph: DependencyGraph):
        """
        Initialize the formatter with a dependency graph.

        Args:
            dependency_graph (DependencyGraph): The analyzed dependency graph
        """
        self.graph = dependency_graph
        self.project_root = dependency_graph.project_root

    def _get_relative_path(self, file_path: str) -> str:
        """
        Get relative path from project root.

        Converts absolute file paths to relative paths from the project root,
        making output more portable and readable.

        Args:
            file_path (str): Absolute file path

        Returns:
            str: Relative path from project root, or original path if outside project

        Examples:
            >>> formatter = OutputFormatter(graph)
            >>> rel_path = formatter._get_relative_path("/project/src/main.ts")
            >>> print(rel_path)  # "src/main.ts"
        """
        try:
            return str(Path(file_path).relative_to(self.project_root))
        except ValueError:
            return file_path

    def _get_file_label(self, file_path: str) -> str:
        """
        Get a clean, normalized label for a file.

        Creates a consistent file label by converting to relative path
        and normalizing path separators for cross-platform compatibility.

        Args:
            file_path (str): File path to create label for

        Returns:
            str: Clean file label with forward slashes

        Examples:
            >>> formatter = OutputFormatter(graph)
            >>> label = formatter._get_file_label("/project/src\\components\\Button.vue")
            >>> print(label)  # "src/components/Button.vue"
        """
        rel_path = self._get_relative_path(file_path)
        return rel_path.replace('\\', '/')


class JSONFormatter(OutputFormatter):
    """
    Format dependency analysis results as structured JSON.

    Produces comprehensive JSON output suitable for programmatic processing,
    API responses, or storage. Includes project statistics, file details,
    dependency relationships, and metadata.

    Examples:
        >>> formatter = JSONFormatter(dependency_graph)
        >>> json_output = formatter.format(include_details=True)
        >>> data = json.loads(json_output)
        >>> print(data['statistics']['total_files'])
    """

    def format(self, include_details: bool = True) -> str:
        """
        Format the dependency graph as structured JSON.

        Creates a comprehensive JSON representation of the dependency graph
        including statistics, file information, and relationships.

        Args:
            include_details (bool): Whether to include detailed import/export information

        Returns:
            str: JSON-formatted dependency analysis results

        Examples:
            >>> formatter = JSONFormatter(graph)
            >>> # Basic output
            >>> basic_json = formatter.format(include_details=False)
            >>>
            >>> # Detailed output with import/export info
            >>> detailed_json = formatter.format(include_details=True)
        """
        result = {
            'project_root': str(self.project_root),
            'statistics': self.graph.get_statistics(),
            'files': {},
            'external_dependencies': self.graph.get_external_dependencies(),
            'circular_dependencies': [
                [self._get_relative_path(f) for f in cycle]
                for cycle in self.graph.find_circular_dependencies()
            ],
            'entry_points': [self._get_relative_path(f) for f in self.graph.get_entry_points()],
            'leaf_nodes': [self._get_relative_path(f) for f in self.graph.get_leaf_nodes()]
        }
        
        # Add file details
        for file_path, node in self.graph.nodes.items():
            rel_path = self._get_relative_path(file_path)
            
            file_info = {
                'path': rel_path,
                'is_entry_point': node.is_entry_point,
                'is_leaf_node': node.is_leaf_node,
                'dependencies': [self._get_relative_path(dep) for dep in self.graph.get_dependencies(file_path)],
                'dependents': [self._get_relative_path(dep) for dep in self.graph.get_dependents(file_path)]
            }
            
            if include_details:
                file_info.update({
                    'imports': [self._format_import_info(imp) for imp in node.imports],
                    'exports': node.exports
                })
            
            result['files'][rel_path] = file_info
        
        return json.dumps(result, indent=2, ensure_ascii=False)
    
    def _format_import_info(self, import_info: ImportInfo) -> Dict:
        """Format import information for JSON output."""
        return {
            'module': self._get_relative_path(import_info.imported_module) 
                     if import_info.imported_module.startswith(str(self.project_root))
                     else import_info.imported_module,
            'type': import_info.import_type.value,
            'dependency_type': import_info.dependency_type.value,
            'imported_names': import_info.imported_names,
            'line_number': import_info.line_number,
            'is_type_only': import_info.is_type_only
        }


class DOTFormatter(OutputFormatter):
    """Format dependency graph as DOT format for Graphviz."""
    
    def format(self, include_external: bool = False, cluster_by_directory: bool = True) -> str:
        """Format the dependency graph as DOT format."""
        lines = ['digraph DependencyGraph {']
        lines.append('  rankdir=TB;')
        lines.append('  node [shape=box, style=filled];')
        lines.append('')
        
        # Define node styles
        lines.extend([
            '  // Node styles',
            '  node [fillcolor=lightblue];',
            '  // Entry points',
            '  node [fillcolor=lightgreen];'
        ])
        
        # Add entry point nodes
        entry_points = self.graph.get_entry_points()
        for entry in entry_points:
            label = self._get_file_label(entry)
            lines.append(f'  "{label}";')
        
        lines.append('  // Leaf nodes')
        lines.append('  node [fillcolor=lightyellow];')
        
        # Add leaf nodes
        leaf_nodes = self.graph.get_leaf_nodes()
        for leaf in leaf_nodes:
            if leaf not in entry_points:
                label = self._get_file_label(leaf)
                lines.append(f'  "{label}";')
        
        lines.append('  // Regular nodes')
        lines.append('  node [fillcolor=lightblue];')
        
        # Add regular nodes
        for file_path in self.graph.nodes:
            if file_path not in entry_points and file_path not in leaf_nodes:
                label = self._get_file_label(file_path)
                lines.append(f'  "{label}";')
        
        lines.append('')
        lines.append('  // Dependencies')
        
        # Add edges
        for source, target, data in self.graph.graph.edges(data=True):
            source_label = self._get_file_label(source)
            target_label = self._get_file_label(target)
            
            # Add edge attributes
            edge_attrs = []
            if 'import_type' in data:
                edge_attrs.append(f'label="{data["import_type"]}"')
            
            attrs_str = f' [{", ".join(edge_attrs)}]' if edge_attrs else ''
            lines.append(f'  "{source_label}" -> "{target_label}"{attrs_str};')
        
        # Add circular dependencies highlighting
        cycles = self.graph.find_circular_dependencies()
        if cycles:
            lines.append('')
            lines.append('  // Circular dependencies')
            for cycle in cycles:
                for i in range(len(cycle)):
                    source = self._get_file_label(cycle[i])
                    target = self._get_file_label(cycle[(i + 1) % len(cycle)])
                    lines.append(f'  "{source}" -> "{target}" [color=red, penwidth=2];')
        
        lines.append('}')
        return '\n'.join(lines)


class MermaidFormatter(OutputFormatter):
    """Format dependency graph as Mermaid diagram syntax."""
    
    def format(self, diagram_type: str = 'flowchart') -> str:
        """Format the dependency graph as Mermaid diagram."""
        if diagram_type == 'flowchart':
            return self._format_flowchart()
        elif diagram_type == 'graph':
            return self._format_graph()
        else:
            raise ValueError(f"Unsupported diagram type: {diagram_type}")
    
    def _format_flowchart(self) -> str:
        """Format as Mermaid flowchart."""
        lines = ['flowchart TD']
        
        # Create node definitions
        node_map = {}
        for i, file_path in enumerate(self.graph.nodes):
            node_id = f"N{i}"
            label = self._get_file_label(file_path)
            node_map[file_path] = node_id
            
            # Determine node style based on type
            if file_path in self.graph.get_entry_points():
                lines.append(f'  {node_id}["{label}"]')
                lines.append(f'  {node_id} --> {node_id}')  # Self-reference for styling
            elif file_path in self.graph.get_leaf_nodes():
                lines.append(f'  {node_id}("{label}")')
            else:
                lines.append(f'  {node_id}["{label}"]')
        
        lines.append('')
        
        # Add edges
        for source, target in self.graph.graph.edges():
            source_id = node_map[source]
            target_id = node_map[target]
            lines.append(f'  {source_id} --> {target_id}')
        
        # Add styling
        lines.extend([
            '',
            '  %% Styling',
            '  classDef entryPoint fill:#90EE90',
            '  classDef leafNode fill:#FFE4B5',
            '  classDef regularNode fill:#ADD8E6'
        ])
        
        # Apply styles
        entry_points = self.graph.get_entry_points()
        leaf_nodes = self.graph.get_leaf_nodes()
        
        for file_path, node_id in node_map.items():
            if file_path in entry_points:
                lines.append(f'  class {node_id} entryPoint')
            elif file_path in leaf_nodes:
                lines.append(f'  class {node_id} leafNode')
            else:
                lines.append(f'  class {node_id} regularNode')
        
        return '\n'.join(lines)
    
    def _format_graph(self) -> str:
        """Format as Mermaid graph."""
        lines = ['graph TD']
        
        # Add nodes and edges
        for source, target in self.graph.graph.edges():
            source_label = self._get_file_label(source).replace('/', '_').replace('.', '_')
            target_label = self._get_file_label(target).replace('/', '_').replace('.', '_')
            
            lines.append(f'  {source_label} --> {target_label}')
        
        return '\n'.join(lines)


class TextTreeFormatter(OutputFormatter):
    """Format dependency graph as simple text-based tree structure."""
    
    def format(self, show_details: bool = False) -> str:
        """Format the dependency graph as text tree."""
        lines = ['Dependency Tree Structure']
        lines.append('=' * 50)
        lines.append('')
        
        # Show statistics first
        stats = self.graph.get_statistics()
        lines.extend([
            f"Total Files: {stats['total_files']}",
            f"Total Dependencies: {stats['total_dependencies']}",
            f"Entry Points: {stats['entry_points']}",
            f"Leaf Nodes: {stats['leaf_nodes']}",
            f"Circular Dependencies: {stats['circular_dependencies']}",
            f"External Packages: {stats['external_packages']}",
            ''
        ])
        
        # Show entry points and their dependency trees
        entry_points = self.graph.get_entry_points()
        
        if entry_points:
            lines.append('Entry Points:')
            lines.append('-' * 20)
            
            for entry_point in entry_points:
                lines.extend(self._format_dependency_tree(entry_point, show_details))
                lines.append('')
        
        # Show circular dependencies if any
        cycles = self.graph.find_circular_dependencies()
        if cycles:
            lines.append('Circular Dependencies:')
            lines.append('-' * 25)
            
            for i, cycle in enumerate(cycles, 1):
                lines.append(f"Cycle {i}:")
                for j, file_path in enumerate(cycle):
                    prefix = "  └─ " if j == len(cycle) - 1 else "  ├─ "
                    lines.append(f"{prefix}{self._get_file_label(file_path)}")
                lines.append('')
        
        # Show external dependencies
        external_deps = self.graph.get_external_dependencies()
        if external_deps:
            lines.append('External Dependencies:')
            lines.append('-' * 25)
            
            for package, files in external_deps.items():
                lines.append(f"{package} (used in {len(files)} files)")
                if show_details:
                    for file_path in files[:5]:  # Show first 5 files
                        lines.append(f"  ├─ {self._get_file_label(file_path)}")
                    if len(files) > 5:
                        lines.append(f"  └─ ... and {len(files) - 5} more")
                lines.append('')
        
        return '\n'.join(lines)
    
    def _format_dependency_tree(self, root_file: str, show_details: bool = False, 
                               visited: Optional[set] = None, prefix: str = "") -> List[str]:
        """Format a dependency tree starting from a root file."""
        if visited is None:
            visited = set()
        
        lines = []
        
        if root_file in visited:
            # Circular dependency detected
            lines.append(f"{prefix}📁 {self._get_file_label(root_file)} (circular)")
            return lines
        
        visited.add(root_file)
        
        # Add current file
        icon = "🚀" if root_file in self.graph.get_entry_points() else "📁"
        lines.append(f"{prefix}{icon} {self._get_file_label(root_file)}")
        
        # Add details if requested
        if show_details and root_file in self.graph.nodes:
            node = self.graph.nodes[root_file]
            if node.imports:
                external_imports = [imp for imp in node.imports if imp.dependency_type.value == 'external']
                if external_imports:
                    lines.append(f"{prefix}   📦 External: {', '.join(set(imp.imported_module for imp in external_imports[:3]))}")
        
        # Add dependencies
        dependencies = self.graph.get_dependencies(root_file)
        
        for i, dep_file in enumerate(dependencies):
            is_last = i == len(dependencies) - 1
            new_prefix = prefix + ("    " if is_last else "│   ")
            child_prefix = prefix + ("└── " if is_last else "├── ")
            
            lines.append(f"{child_prefix}")
            lines.extend(self._format_dependency_tree(dep_file, show_details, visited.copy(), new_prefix))
        
        return lines
